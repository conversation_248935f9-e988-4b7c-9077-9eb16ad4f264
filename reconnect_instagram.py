#!/usr/bin/env python3
"""
Script to reconnect Instagram account
"""

import sys
import os
import json
import getpass

# Add integrations to path
sys.path.append('integrations')

def reconnect_instagram():
    """Reconnect Instagram account"""
    print("🔄 Instagram Account Reconnection Tool")
    print("=" * 50)
    
    try:
        # Import Instagram API
        from instagram_integration.instagram_api import InstagramMessaging
        
        print("✅ Instagram API loaded")
        
        # Initialize
        instagram = InstagramMessaging()
        
        if not instagram.unipile_client:
            print("❌ Unipile client not available")
            return False
        
        print(f"✅ Unipile client ready (API key: {instagram.unipile_client.api_key[:10]}...)")
        
        # Get credentials
        print("\n🔐 Enter your Instagram credentials:")
        username = input("Username/Email: ").strip()
        
        if not username:
            print("❌ Username is required")
            return False
        
        password = getpass.getpass("Password: ")
        
        if not password:
            print("❌ Password is required")
            return False
        
        print(f"\n🔗 Connecting Instagram account for: {username}")
        
        # Connect account
        result = instagram.connect_account(username, password)
        
        print(f"\n📋 Connection result:")
        print(json.dumps(result, indent=2))
        
        if result.get("success"):
            print("\n✅ Account connected successfully!")
            account_id = result.get("account_id")
            print(f"Account ID: {account_id}")
            
            # Test account retrieval
            print("\n🔍 Testing account retrieval...")
            account_info = instagram.get_account_info()
            
            if "error" not in account_info:
                print("✅ Account is now accessible!")
                print(f"Account info: {json.dumps(account_info, indent=2)}")
            else:
                print("⚠️  Account connected but not immediately accessible")
                print("This is normal - it may take a few minutes to sync")
                
        elif result.get("checkpoint_required"):
            print("\n🔐 Verification required!")
            checkpoint_type = result.get("checkpoint_type", "Unknown")
            print(f"Checkpoint type: {checkpoint_type}")
            print(f"Account ID: {result.get('account_id')}")
            print(f"Message: {result.get('message', 'Complete verification')}")
            
            print("\n💡 Next steps:")
            print("1. Check your email/SMS for verification code")
            print("2. Use the HTML interface to complete verification")
            print("3. Or complete verification in Unipile dashboard")
            
        else:
            print("\n❌ Connection failed")
            error = result.get("error", result.get("message", "Unknown error"))
            print(f"Error: {error}")
            
            print("\n💡 Troubleshooting:")
            print("1. Check your Instagram credentials")
            print("2. Ensure your account is Business/Creator")
            print("3. Try connecting via Unipile dashboard directly")
        
        return result.get("success", False)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_current_status():
    """Check current Instagram connection status"""
    print("\n📊 Current Connection Status")
    print("=" * 30)
    
    try:
        from instagram_integration.instagram_api import InstagramMessaging
        
        instagram = InstagramMessaging()
        
        # Get status
        status = instagram.get_connection_status()
        print(f"Status: {json.dumps(status, indent=2)}")
        
        # Get account info
        account_info = instagram.get_account_info()
        print(f"\nAccount info: {json.dumps(account_info, indent=2)}")
        
    except Exception as e:
        print(f"❌ Error checking status: {e}")

if __name__ == "__main__":
    print("🔧 Instagram Reconnection Tool")
    print("🎯 This will help you reconnect your Instagram account")
    print()
    
    # Check current status first
    check_current_status()
    
    print("\n" + "="*50)
    
    # Ask if user wants to reconnect
    response = input("\nDo you want to reconnect your Instagram account? (y/n): ").strip().lower()
    
    if response in ['y', 'yes']:
        success = reconnect_instagram()
        
        if success:
            print("\n🎉 Reconnection successful!")
            print("Your Instagram account should now be accessible.")
        else:
            print("\n⚠️  Reconnection incomplete")
            print("You may need to complete verification steps.")
    else:
        print("\n💡 To reconnect later:")
        print("1. Run this script again")
        print("2. Or use the Unipile dashboard")
        print("3. Or use the HTML interface when server is running")
    
    print("\n🏁 Done!")
