#!/usr/bin/env python3
"""
Test Instagram Integration
Verify that the Instagram integration is working properly
"""

import sys
import os

def test_instagram_import():
    """Test Instagram API import"""
    print("🔧 Testing Instagram Import...")
    print("=" * 50)
    
    try:
        sys.path.append('integrations')
        from instagram_integration.instagram_api import InstagramMessaging
        print("✅ Instagram API imported successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to import Instagram API: {e}")
        return False

def test_instagram_initialization():
    """Test Instagram API initialization"""
    print("\n🔧 Testing Instagram Initialization...")
    print("=" * 50)
    
    try:
        sys.path.append('integrations')
        from instagram_integration.instagram_api import InstagramMessaging
        
        instagram = InstagramMessaging()
        print("✅ Instagram API initialized successfully")
        
        # Test if Unipile client is available
        if instagram.unipile_client:
            print("✅ Unipile client available")
        else:
            print("⚠️ Unipile client not available (expected if no API key)")
        
        return True
    except Exception as e:
        print(f"❌ Failed to initialize Instagram API: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_instagram_methods():
    """Test Instagram API methods"""
    print("\n🔧 Testing Instagram Methods...")
    print("=" * 50)
    
    try:
        sys.path.append('integrations')
        from instagram_integration.instagram_api import InstagramMessaging
        
        instagram = InstagramMessaging()
        
        # Test connection status
        status = instagram.get_connection_status()
        print(f"✅ Connection status method works: {type(status)}")
        
        # Test account info
        account_info = instagram.get_account_info()
        print(f"✅ Account info method works: {type(account_info)}")
        
        # Test is_configured
        configured = instagram.is_configured()
        print(f"✅ Is configured method works: {configured}")
        
        return True
    except Exception as e:
        print(f"❌ Failed to test Instagram methods: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_endpoints():
    """Test API endpoints integration"""
    print("\n🔧 Testing API Endpoints Integration...")
    print("=" * 50)
    
    try:
        sys.path.append('integrations')
        from api_endpoints import app
        print("✅ API endpoints imported successfully")
        
        # Check if Instagram endpoints are registered
        routes = [route.path for route in app.routes]
        instagram_routes = [route for route in routes if 'instagram' in route]
        
        if instagram_routes:
            print(f"✅ Instagram routes found: {len(instagram_routes)} routes")
            for route in instagram_routes[:5]:  # Show first 5
                print(f"   - {route}")
            if len(instagram_routes) > 5:
                print(f"   ... and {len(instagram_routes) - 5} more")
        else:
            print("❌ No Instagram routes found")
            return False
        
        return True
    except Exception as e:
        print(f"❌ Failed to test API endpoints: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_structure():
    """Test Instagram file structure"""
    print("\n🔧 Testing Instagram File Structure...")
    print("=" * 50)
    
    required_files = [
        "integrations/instagram_integration/instagram_api.py",
        "integrations/instagram_integration/instagram_auth.html",
        "integrations/instagram_integration/config.json"
    ]
    
    all_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ File exists: {file_path}")
        else:
            print(f"❌ File missing: {file_path}")
            all_exist = False
    
    return all_exist

def test_config_file():
    """Test Instagram configuration file"""
    print("\n🔧 Testing Instagram Configuration...")
    print("=" * 50)
    
    try:
        import json
        with open("integrations/instagram_integration/config.json", 'r') as f:
            config = json.load(f)
        
        required_keys = ["unipile", "rate_limit", "settings", "features"]
        for key in required_keys:
            if key in config:
                print(f"✅ Config key exists: {key}")
            else:
                print(f"❌ Config key missing: {key}")
                return False
        
        # Check Instagram-specific features
        features = config.get("features", {})
        if features.get("comment_replies"):
            print("✅ Comment replies feature enabled")
        if features.get("story_interactions"):
            print("✅ Story interactions feature enabled")
        if not features.get("bulk_messaging", True):
            print("✅ Bulk messaging correctly disabled")
        
        return True
    except Exception as e:
        print(f"❌ Failed to test config file: {e}")
        return False

def main():
    """Run all Instagram integration tests"""
    print("🚀 Instagram Integration Test Suite")
    print("=" * 60)
    
    tests = [
        ("File Structure", test_file_structure),
        ("Configuration", test_config_file),
        ("Instagram Import", test_instagram_import),
        ("Instagram Initialization", test_instagram_initialization),
        ("Instagram Methods", test_instagram_methods),
        ("API Endpoints", test_api_endpoints),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    
    passed = 0
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        emoji = "✅" if result else "❌"
        print(f"{emoji} {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 All tests passed! Instagram integration is working correctly!")
        print("\n💡 You can now:")
        print("   • Start the server: cd integrations && python api_endpoints.py")
        print("   • Access main dashboard: http://localhost:8000/")
        print("   • Access Instagram auth: http://localhost:8000/instagram/instagram_auth.html")
        print("   • Connect your Instagram Business/Creator account")
        print("   • Use comment replies and story interactions")
        
        print("\n📋 Instagram Features Available:")
        print("   • ✅ Account connection via Unipile API")
        print("   • ✅ Comment replies (primary feature)")
        print("   • ✅ Story interactions")
        print("   • ✅ Connection status monitoring")
        print("   • ✅ Account information display")
        print("   • ✅ Rate limiting compliance")
        
        print("\n⚠️ Instagram Limitations:")
        print("   • Business/Creator accounts only")
        print("   • 24-hour messaging window")
        print("   • No bulk messaging")
        print("   • Focus on comment replies recommended")
        
    else:
        print(f"\n❌ {len(results) - passed} tests failed. Please check the errors above.")
        print("   Fix the issues and run the test again.")

if __name__ == "__main__":
    main()
