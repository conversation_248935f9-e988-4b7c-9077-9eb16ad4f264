#!/usr/bin/env python3
"""
Simple server for Instagram integration testing
"""

import sys
import os

# Add integrations to path
sys.path.append('integrations')

def start_server():
    """Start the server"""
    try:
        print("🚀 Starting Instagram Integration Server...")
        print("📍 Server will be available at: http://localhost:8000")
        print("📱 Instagram page: http://localhost:8000/integrations/instagram_integration/instagram_auth.html")
        print("-" * 60)
        
        # Import FastAPI app
        from api_endpoints import app
        
        print("✅ FastAPI app imported successfully")
        
        # Start with uvicorn
        import uvicorn
        
        print("🔄 Starting server...")
        
        uvicorn.run(
            app,
            host="127.0.0.1",
            port=8000,
            log_level="info",
            reload=False
        )
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure you have installed:")
        print("   pip install fastapi uvicorn")
        return False
    except Exception as e:
        print(f"❌ Server error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 Simple Instagram Server")
    print("=" * 30)
    
    start_server()
