#!/usr/bin/env python3
"""
Direct test of Instagram connection without server
"""

import sys
import os
import json

# Add integrations to path
sys.path.append('integrations')

def test_instagram_connection():
    """Test Instagram connection directly"""
    print("🧪 Testing Instagram Connection Directly")
    print("=" * 50)
    
    try:
        # Test 1: Import Instagram API
        print("📦 Importing Instagram API...")
        from instagram_integration.instagram_api import InstagramMessaging
        print("✅ Instagram API imported successfully")
        
        # Test 2: Initialize Instagram API
        print("\n🔧 Initializing Instagram API...")
        instagram = InstagramMessaging()
        print("✅ Instagram API initialized")
        
        # Test 3: Check Unipile client
        print(f"\n🔗 Unipile client available: {bool(instagram.unipile_client)}")
        if instagram.unipile_client:
            print(f"🔑 API key configured: {bool(instagram.unipile_client.api_key)}")
            print(f"🌐 API key (first 10 chars): {instagram.unipile_client.api_key[:10]}...")
        
        # Test 4: Get connection status
        print("\n📊 Getting connection status...")
        status = instagram.get_connection_status()
        print(f"Connection status: {json.dumps(status, indent=2)}")
        
        # Test 5: Get account info
        print("\n👤 Getting account info...")
        account_info = instagram.get_account_info()
        print(f"Account info: {json.dumps(account_info, indent=2)}")
        
        # Test 6: Check if we can find the specific account ID
        expected_account_id = "qGtnQgtJQ7SfWNHseeFpDQ"
        print(f"\n🔍 Looking for account ID: {expected_account_id}")
        
        if "error" not in account_info:
            found_id = account_info.get("id") or account_info.get("account", {}).get("id")
            if found_id == expected_account_id:
                print("✅ Found matching account ID!")
            else:
                print(f"❌ Account ID mismatch. Found: {found_id}")
        else:
            print("❌ No account found")
        
        # Test 7: Direct Unipile API call
        print("\n🌐 Testing direct Unipile API call...")
        if instagram.unipile_client:
            accounts = instagram.unipile_client.get_accounts()
            print(f"Direct API response: {json.dumps(accounts, indent=2)}")
            
            # Check for Instagram accounts
            all_accounts = accounts.get("items", accounts.get("accounts", []))
            instagram_accounts = [acc for acc in all_accounts if acc.get("type") == "INSTAGRAM"]
            
            print(f"\n📈 Summary:")
            print(f"Total accounts: {len(all_accounts)}")
            print(f"Instagram accounts: {len(instagram_accounts)}")
            
            if instagram_accounts:
                print("\n✅ Instagram accounts found:")
                for i, acc in enumerate(instagram_accounts):
                    print(f"  Account {i+1}:")
                    print(f"    ID: {acc.get('id', 'N/A')}")
                    print(f"    Username: {acc.get('username', 'N/A')}")
                    print(f"    Type: {acc.get('type', 'N/A')}")
            else:
                print("\n❌ No Instagram accounts found in API response")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_account_reconnection():
    """Test reconnecting the Instagram account"""
    print("\n🔄 Testing Account Reconnection")
    print("=" * 50)
    
    # Note: This would require actual credentials
    print("💡 To test account reconnection, you would need to:")
    print("1. Use your Instagram username/email and password")
    print("2. Call instagram.connect_account(username, password)")
    print("3. Handle any verification checkpoints")
    print("4. Check if the account appears in the account list")
    
    print("\n⚠️  For security, this test doesn't include actual credential testing")
    print("   Use the HTML interface to test account reconnection")

if __name__ == "__main__":
    print("🔧 Instagram Direct Connection Test")
    print("🎯 This test will check your Instagram connection without the server")
    print()
    
    # Run the main test
    success = test_instagram_connection()
    
    if success:
        print("\n" + "="*50)
        test_account_reconnection()
    
    print("\n🏁 Test completed!")
    
    if not success:
        print("\n💡 If you see errors above, try:")
        print("1. Check that all dependencies are installed")
        print("2. Verify the API key is correct")
        print("3. Test the connection using the HTML interface")
