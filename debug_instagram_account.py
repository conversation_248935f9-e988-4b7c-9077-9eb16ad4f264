#!/usr/bin/env python3
"""
Debug script to check Instagram account connection issue
"""

import sys
import os
import json
import requests

# Add integrations to path
sys.path.append('integrations')

def test_unipile_direct():
    """Test Unipile API directly"""
    print("🔍 Testing Unipile API directly...")
    
    api_key = "iGJsHDIR.sr6Pg30nB1cKUPC6eDY8tzVx+Opu+t+c6wQiKAEz2Ek="
    base_url = "https://api8.unipile.com:13814/api/v1"
    
    headers = {
        "X-API-KEY": api_key,
        "accept": "application/json",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(f"{base_url}/accounts", headers=headers, timeout=30)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Raw Response: {json.dumps(data, indent=2)}")
            
            # Check for accounts
            all_accounts = data.get("items", data.get("accounts", []))
            instagram_accounts = [acc for acc in all_accounts if acc.get("type") == "INSTAGRAM"]
            
            print(f"\n📊 Summary:")
            print(f"Total accounts: {len(all_accounts)}")
            print(f"Instagram accounts: {len(instagram_accounts)}")
            
            if instagram_accounts:
                print(f"\n✅ Instagram accounts found:")
                for i, acc in enumerate(instagram_accounts):
                    print(f"  Account {i+1}:")
                    print(f"    ID: {acc.get('id', 'N/A')}")
                    print(f"    Account ID: {acc.get('account_id', 'N/A')}")
                    print(f"    Username: {acc.get('username', 'N/A')}")
                    print(f"    Name: {acc.get('name', 'N/A')}")
                    print(f"    Type: {acc.get('type', 'N/A')}")
                    print(f"    Provider: {acc.get('provider', 'N/A')}")
                    print(f"    All keys: {list(acc.keys())}")
                    print()
            else:
                print(f"\n❌ No Instagram accounts found")
                if all_accounts:
                    print("Available account types:")
                    for acc in all_accounts:
                        print(f"  - {acc.get('type', 'Unknown')}: {acc.get('id', 'N/A')}")
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def test_instagram_api():
    """Test Instagram API class"""
    print("\n🧪 Testing Instagram API class...")
    
    try:
        from instagram_integration.instagram_api import InstagramMessaging
        
        instagram = InstagramMessaging()
        print(f"✅ Instagram API initialized")
        print(f"Unipile client available: {bool(instagram.unipile_client)}")
        
        if instagram.unipile_client:
            print(f"API key configured: {bool(instagram.unipile_client.api_key)}")
            
            # Test get_account_info
            print("\n📋 Testing get_account_info...")
            result = instagram.get_account_info()
            print(f"Account info result: {json.dumps(result, indent=2)}")
            
            # Test connection status
            print("\n🔗 Testing connection status...")
            status = instagram.get_connection_status()
            print(f"Connection status: {json.dumps(status, indent=2)}")
        
    except Exception as e:
        print(f"❌ Instagram API test failed: {e}")
        import traceback
        traceback.print_exc()

def test_api_endpoint():
    """Test the API endpoint"""
    print("\n🌐 Testing API endpoint...")
    
    try:
        response = requests.get('http://localhost:8000/api/instagram/account-info', timeout=10)
        print(f'Status Code: {response.status_code}')
        
        if response.status_code == 200:
            data = response.json()
            print(f'Response: {json.dumps(data, indent=2)}')
        else:
            print(f'Error Response: {response.text}')
            
    except requests.exceptions.ConnectionError:
        print("❌ Server not running or not accessible at localhost:8000")
    except Exception as e:
        print(f"❌ API Error: {e}")

if __name__ == "__main__":
    print("🔧 Instagram Account Debug Script")
    print("=" * 50)
    
    # Test 1: Direct Unipile API
    test_unipile_direct()
    
    # Test 2: Instagram API class
    test_instagram_api()
    
    # Test 3: API endpoint (if server is running)
    test_api_endpoint()
    
    print("\n🏁 Debug completed!")
    print("\nExpected Account ID: qGtnQgtJQ7SfWNHseeFpDQ")
    print("If this account ID appears above, the connection is working!")
