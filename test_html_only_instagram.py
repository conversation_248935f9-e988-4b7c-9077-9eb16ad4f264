#!/usr/bin/env python3
"""
Test Instagram HTML-only connection methods
This script tests all the HTML-only connection methods we've implemented
"""

import sys
import os
import json
import time

# Add integrations to path
sys.path.append('integrations')

def test_instagram_connection_methods():
    """Test all Instagram connection methods available through HTML"""
    print("🧪 Testing Instagram HTML-Only Connection Methods")
    print("=" * 60)
    
    try:
        from instagram_integration.instagram_api import InstagramMessaging
        
        print("📷 Initializing Instagram API...")
        instagram = InstagramMessaging()
        
        if not instagram.unipile_client:
            print("❌ Unipile client not available")
            return False
        
        print("✅ Instagram API initialized successfully")
        print(f"🔑 API Key: {instagram.unipile_client.api_key[:20]}...")
        
        # Test 1: Current account status
        print("\n📊 Test 1: Current Account Status")
        print("-" * 40)
        accounts = instagram.unipile_client.get_accounts()
        instagram_accounts = [acc for acc in accounts.get('items', []) 
                            if acc.get('type', '').upper() == 'INSTAGRAM']
        
        print(f"Total accounts: {len(accounts.get('items', []))}")
        print(f"Instagram accounts: {len(instagram_accounts)}")
        
        if instagram_accounts:
            print("✅ Instagram accounts found:")
            for i, acc in enumerate(instagram_accounts):
                print(f"  {i+1}. {acc.get('username', 'N/A')} (ID: {acc.get('id', 'N/A')})")
        else:
            print("❌ No Instagram accounts found - connection needed")
        
        # Test 2: QR Code Generation
        print("\n📱 Test 2: QR Code Generation")
        print("-" * 40)
        qr_result = instagram.generate_qr_code()
        print(f"QR generation result: {json.dumps(qr_result, indent=2)}")
        
        if qr_result.get("success"):
            print("✅ QR code generation works")
        else:
            print("⚠️ QR code generation not available")
        
        # Test 3: Enhanced Connection with Dummy Credentials
        print("\n🔐 Test 3: Enhanced Connection Method")
        print("-" * 40)
        connect_result = instagram.connect_account("test_user", "test_password")
        print(f"Connection result keys: {list(connect_result.keys())}")
        
        if connect_result.get("html_only_solution"):
            print("✅ HTML-only solution provided")
            if connect_result.get("troubleshooting"):
                print("✅ Troubleshooting information available")
        elif connect_result.get("qr_code_available"):
            print("✅ QR code fallback available")
        else:
            print("⚠️ Standard error response")
        
        # Test 4: Alternative Connection Methods
        print("\n🔄 Test 4: Alternative Connection Methods")
        print("-" * 40)
        
        # Test simplified connection
        simplified_result = instagram._try_simplified_connection("test_user", "test_password")
        print(f"Simplified connection: {simplified_result.get('message', 'No message')}")
        
        # Test alternative methods
        alternative_result = instagram._try_alternative_connection_methods("test_user", "test_password")
        print(f"Alternative methods: {alternative_result.get('message', 'No message')}")
        
        if alternative_result.get("html_only_solution"):
            print("✅ HTML-only solution provided by alternative methods")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_endpoints():
    """Test the API endpoints that support HTML-only solutions"""
    print("\n🌐 Testing API Endpoints")
    print("=" * 40)
    
    try:
        import requests
        
        # Test QR generation endpoint
        print("Testing QR generation endpoint...")
        try:
            response = requests.post('http://localhost:8000/api/instagram/generate-qr', 
                                   headers={'Content-Type': 'application/json'}, 
                                   timeout=10)
            print(f"QR endpoint status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"QR endpoint response: {data.get('message', 'No message')}")
            else:
                print(f"QR endpoint error: {response.text}")
        except requests.exceptions.ConnectionError:
            print("❌ Server not running - cannot test API endpoints")
            return False
        
        # Test connection endpoint with dummy data
        print("\nTesting connection endpoint...")
        try:
            response = requests.post('http://localhost:8000/api/instagram/connect-account',
                                   json={'username': 'test_user', 'password': 'test_password'},
                                   headers={'Content-Type': 'application/json'},
                                   timeout=10)
            print(f"Connection endpoint status: {response.status_code}")
            if response.status_code in [200, 400]:  # Both are valid for our test
                data = response.json()
                print(f"Connection endpoint response: {data.get('message', 'No message')}")
                
                if data.get('html_only_solution'):
                    print("✅ HTML-only solution provided by API")
                elif data.get('qr_code_available'):
                    print("✅ QR code fallback provided by API")
            else:
                print(f"Connection endpoint error: {response.text}")
        except Exception as e:
            print(f"Connection endpoint error: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ API endpoint test error: {e}")
        return False

def main():
    """Run all HTML-only tests"""
    print("🚀 Instagram HTML-Only Connection Test Suite")
    print("=" * 60)
    
    # Test 1: Connection methods
    test1_passed = test_instagram_connection_methods()
    
    # Test 2: API endpoints (if server is running)
    test2_passed = test_api_endpoints()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Summary:")
    print(f"✅ Connection Methods: {'PASS' if test1_passed else 'FAIL'}")
    print(f"✅ API Endpoints: {'PASS' if test2_passed else 'FAIL'}")
    
    if test1_passed:
        print("\n🎉 HTML-only Instagram connection methods are working!")
        print("\n💡 Available Methods:")
        print("1. ✅ Enhanced credential connection with fallbacks")
        print("2. ✅ QR code generation (if supported)")
        print("3. ✅ Alternative connection methods")
        print("4. ✅ Comprehensive error handling and troubleshooting")
        print("5. ✅ HTML-only solutions without dashboard dependency")
        
        print("\n🔧 Next Steps:")
        print("1. Start your server: python main.py")
        print("2. Open Instagram auth page in browser")
        print("3. Try connecting with your real Instagram credentials")
        print("4. If credential method fails, try QR code method")
        print("5. Follow the troubleshooting suggestions provided")
        
    else:
        print("\n❌ Some tests failed. Please check the errors above.")
        print("\n🛠️ Troubleshooting:")
        print("1. Ensure all dependencies are installed")
        print("2. Check your Unipile API key")
        print("3. Verify internet connectivity")

if __name__ == "__main__":
    main()
