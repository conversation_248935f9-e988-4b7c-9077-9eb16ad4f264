#!/usr/bin/env python3
"""
Verify all API keys have been updated across all integrations
"""

import sys
import os
import json

# Add integrations to path
sys.path.append('integrations')

def check_config_files():
    """Check all config files for API key updates"""
    print("🔧 Checking Configuration Files")
    print("=" * 50)
    
    new_api_key = "bs8WGcCp./sF0oPlTIohHPT6hI+hsyO2HFyGzKIKdfC36gAvXTk0="
    old_api_keys = [
        "iGJsHDIR.sr6Pg30nB1cKUPC6eDY8tzVx+Opu+t+c6wQiKAEz2Ek=",
        "K0Py2YdG.SazddZm5laRo0Bk9kZ0cKnNt8PLt/AJj15NEGsM7lrk="
    ]
    
    config_files = [
        "integrations/instagram_integration/config.json",
        "integrations/linkedin_integration/config.json", 
        "integrations/unified_config.json",
        "integrations/unified_messaging_config.json"
    ]
    
    all_updated = True
    
    for config_file in config_files:
        print(f"\n📄 Checking {config_file}...")
        
        if not os.path.exists(config_file):
            print(f"   ⚠️ File not found: {config_file}")
            continue
            
        try:
            with open(config_file, 'r') as f:
                content = f.read()
                
            # Check for old API keys
            has_old_key = False
            for old_key in old_api_keys:
                if old_key in content:
                    print(f"   ❌ Found old API key: {old_key[:20]}...")
                    has_old_key = True
                    all_updated = False
            
            # Check for new API key
            if new_api_key in content:
                print(f"   ✅ New API key found: {new_api_key[:20]}...")
            else:
                print(f"   ⚠️ New API key not found")
                
            if not has_old_key and new_api_key in content:
                print(f"   ✅ {config_file} - UPDATED")
            elif not has_old_key:
                print(f"   ⚠️ {config_file} - NO API KEY")
            else:
                print(f"   ❌ {config_file} - NEEDS UPDATE")
                
        except Exception as e:
            print(f"   ❌ Error reading {config_file}: {e}")
            all_updated = False
    
    return all_updated

def test_integrations():
    """Test that integrations are using the new API key"""
    print("\n🔧 Testing Integration API Keys")
    print("=" * 50)
    
    new_api_key = "bs8WGcCp./sF0oPlTIohHPT6hI+hsyO2HFyGzKIKdfC36gAvXTk0="
    
    # Test Instagram
    print("\n📷 Testing Instagram Integration...")
    try:
        from instagram_integration.instagram_api import InstagramMessaging
        instagram = InstagramMessaging()
        
        if instagram.unipile_client:
            current_key = instagram.unipile_client.api_key
            if current_key == new_api_key:
                print(f"   ✅ Instagram using new API key: {current_key[:20]}...")
            else:
                print(f"   ❌ Instagram using old API key: {current_key[:20]}...")
                return False
        else:
            print(f"   ❌ Instagram Unipile client not initialized")
            return False
            
    except Exception as e:
        print(f"   ❌ Instagram test failed: {e}")
        return False
    
    # Test Telegram
    print("\n📱 Testing Telegram Integration...")
    try:
        from telegram_integration.telegram_api import TelegramMessaging
        telegram = TelegramMessaging()
        
        if telegram.unipile_client:
            current_key = telegram.unipile_client.api_key
            if current_key == new_api_key:
                print(f"   ✅ Telegram using new API key: {current_key[:20]}...")
            else:
                print(f"   ❌ Telegram using old API key: {current_key[:20]}...")
                return False
        else:
            print(f"   ❌ Telegram Unipile client not initialized")
            return False
            
    except Exception as e:
        print(f"   ❌ Telegram test failed: {e}")
        return False
    
    # Test UnipileAPI directly
    print("\n🔗 Testing UnipileAPI Default...")
    try:
        from unipile_api import UnipileAPI
        unipile = UnipileAPI()  # Should use new default
        
        if unipile.api_key == new_api_key:
            print(f"   ✅ UnipileAPI using new default key: {unipile.api_key[:20]}...")
        else:
            print(f"   ❌ UnipileAPI using old default key: {unipile.api_key[:20]}...")
            return False
            
    except Exception as e:
        print(f"   ❌ UnipileAPI test failed: {e}")
        return False
    
    return True

def test_api_connection():
    """Test that the new API key actually works"""
    print("\n🌐 Testing API Connection")
    print("=" * 50)
    
    try:
        from unipile_api import UnipileAPI
        
        new_api_key = "bs8WGcCp./sF0oPlTIohHPT6hI+hsyO2HFyGzKIKdfC36gAvXTk0="
        unipile = UnipileAPI(api_key=new_api_key)
        
        print(f"🔑 Testing API key: {new_api_key[:20]}...")
        
        # Test connection
        accounts = unipile.get_accounts(force_refresh=True)
        
        if "error" in accounts:
            error_msg = accounts.get("error", "Unknown error")
            if "401" in str(error_msg) or "Unauthorized" in str(error_msg):
                print(f"   ❌ API key is invalid or unauthorized")
                return False
            else:
                print(f"   ⚠️ API error (but key seems valid): {error_msg}")
                return True  # Key is valid, just other issues
        else:
            print(f"   ✅ API connection successful")
            print(f"   📊 Total accounts: {len(accounts.get('items', []))}")
            
            # Show account details
            for i, acc in enumerate(accounts.get('items', [])):
                account_type = acc.get('type', 'Unknown')
                username = acc.get('username', 'N/A')
                print(f"      Account {i+1}: {account_type} - {username}")
            
            return True
            
    except Exception as e:
        print(f"   ❌ Connection test failed: {e}")
        return False

def main():
    """Run all verification tests"""
    print("🚀 API Key Update Verification")
    print("=" * 60)
    
    # Test 1: Check config files
    config_updated = check_config_files()
    
    # Test 2: Check integrations
    integrations_updated = test_integrations()
    
    # Test 3: Test API connection
    api_works = test_api_connection()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Verification Summary:")
    print(f"✅ Config Files Updated: {'PASS' if config_updated else 'FAIL'}")
    print(f"✅ Integrations Updated: {'PASS' if integrations_updated else 'FAIL'}")
    print(f"✅ API Connection Works: {'PASS' if api_works else 'FAIL'}")
    
    if config_updated and integrations_updated and api_works:
        print("\n🎉 All API keys successfully updated!")
        print("\n💡 Your integrations should now work with the new API key:")
        print("   • Instagram integration ready")
        print("   • Telegram integration ready") 
        print("   • LinkedIn integration ready")
        print("   • All Unipile connections using new key")
        print("\n🚀 You can now try connecting accounts via the HTML interfaces!")
    else:
        print("\n❌ Some issues found:")
        if not config_updated:
            print("   • Some config files still have old API keys")
        if not integrations_updated:
            print("   • Some integrations not using new API key")
        if not api_works:
            print("   • New API key doesn't work or has issues")
        
        print("\n🛠️ Next steps:")
        print("   1. Check the specific errors above")
        print("   2. Verify the API key is correct")
        print("   3. Ensure API key has proper permissions")

if __name__ == "__main__":
    main()
