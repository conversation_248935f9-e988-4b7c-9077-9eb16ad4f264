#!/usr/bin/env python3
"""
Verify Dashboard Cleanup
Check that WhatsApp Guide, WhatsApp Test, and Telegram Bot Tester have been removed
"""

import sys
import os

def check_dashboard_content():
    """Check that removed elements are no longer in the dashboard"""
    print("🔧 Checking Dashboard Content...")
    print("=" * 50)
    
    try:
        with open('integrations/api_endpoints.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for removed elements
        removed_elements = [
            'whatsapp_test.html',
            'whatsapp_messaging_guide.html',
            'bot_tester.html',
            'WhatsApp Test',
            'WhatsApp Guide',
            'Telegram Bot Tester'
        ]
        
        found_elements = []
        for element in removed_elements:
            if element in content:
                found_elements.append(element)
        
        if found_elements:
            print(f"❌ Still found removed elements: {found_elements}")
            return False
        else:
            print("✅ All removed elements successfully cleaned from dashboard")
            return True
            
    except Exception as e:
        print(f"❌ Error checking dashboard content: {e}")
        return False

def check_api_endpoints():
    """Check that removed API endpoints are gone"""
    print("\n🔧 Checking API Endpoints...")
    print("=" * 50)
    
    try:
        with open('integrations/api_endpoints.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for removed endpoints
        removed_endpoints = [
            '@app.get("/api/whatsapp/test")',
            'test_whatsapp_connection',
            'TelegramTestMessageRequest',
            'TelegramBulkMessageRequest'
        ]
        
        found_endpoints = []
        for endpoint in removed_endpoints:
            # Count occurrences
            count = content.count(endpoint)
            if endpoint == 'TelegramTestMessageRequest' or endpoint == 'TelegramBulkMessageRequest':
                # These should exist once (original) but not duplicated
                if count > 1:
                    found_endpoints.append(f"{endpoint} (found {count} times, should be 1)")
            elif endpoint == 'test_whatsapp_connection':
                # This should be completely removed
                if count > 0:
                    found_endpoints.append(f"{endpoint} (found {count} times, should be 0)")
            else:
                if count > 0:
                    found_endpoints.append(endpoint)
        
        if found_endpoints:
            print(f"❌ Issues with endpoints: {found_endpoints}")
            return False
        else:
            print("✅ All API endpoints properly cleaned")
            return True
            
    except Exception as e:
        print(f"❌ Error checking API endpoints: {e}")
        return False

def check_remaining_platforms():
    """Check that all intended platforms remain"""
    print("\n🔧 Checking Remaining Platforms...")
    print("=" * 50)
    
    try:
        with open('integrations/api_endpoints.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for required platforms
        required_platforms = [
            'whatsapp/whatsapp_auth.html',
            'telegram/telegram_auth.html',
            'facebook/facebook_auth.html',
            'instagram/instagram_auth.html',
            'linkedin/linkedin_auth.html',
            'tiktok/tiktok_auth.html'
        ]
        
        missing_platforms = []
        for platform in required_platforms:
            if platform not in content:
                missing_platforms.append(platform)
        
        if missing_platforms:
            print(f"❌ Missing platforms: {missing_platforms}")
            return False
        else:
            print("✅ All required platforms present in dashboard")
            return True
            
    except Exception as e:
        print(f"❌ Error checking platforms: {e}")
        return False

def check_server_startup():
    """Check that server can start without errors"""
    print("\n🔧 Checking Server Startup...")
    print("=" * 50)
    
    try:
        sys.path.append('integrations')
        from api_endpoints import app
        
        print("✅ Server imports successfully")
        
        # Check if FastAPI app is created
        if app:
            print("✅ FastAPI app created successfully")
            
            # Count routes
            routes = [route.path for route in app.routes]
            platform_routes = [route for route in routes if any(platform in route for platform in ['whatsapp', 'telegram', 'facebook', 'instagram', 'linkedin', 'tiktok'])]
            
            print(f"✅ Found {len(platform_routes)} platform-related routes")
            return True
        else:
            print("❌ FastAPI app not created")
            return False
            
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all verification checks"""
    print("🚀 Dashboard Cleanup Verification")
    print("=" * 60)
    
    # Run all checks
    dashboard_clean = check_dashboard_content()
    endpoints_clean = check_api_endpoints()
    platforms_intact = check_remaining_platforms()
    server_works = check_server_startup()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Verification Summary:")
    print(f"✅ Dashboard Content Clean: {'PASS' if dashboard_clean else 'FAIL'}")
    print(f"✅ API Endpoints Clean: {'PASS' if endpoints_clean else 'FAIL'}")
    print(f"✅ Platforms Intact: {'PASS' if platforms_intact else 'FAIL'}")
    print(f"✅ Server Startup Works: {'PASS' if server_works else 'FAIL'}")
    
    if all([dashboard_clean, endpoints_clean, platforms_intact, server_works]):
        print("\n🎉 Dashboard Cleanup Successfully Verified!")
        print("\n💡 Current dashboard shows only:")
        print("   • ✅ WhatsApp - Main authentication")
        print("   • ✅ Telegram - QR code authentication")
        print("   • ✅ Facebook - OAuth integration")
        print("   • ✅ Instagram - Comment replies & story interactions")
        print("   • ✅ LinkedIn - Professional messaging")
        print("   • ✅ TikTok - Content interactions")
        
        print("\n🗑️ Successfully removed:")
        print("   • ❌ WhatsApp Test (broken link)")
        print("   • ❌ WhatsApp Guide (broken link)")
        print("   • ❌ Telegram Bot Tester (broken link)")
        print("   • ❌ Duplicate API endpoints")
        
        print("\n🚀 Your clean dashboard is ready!")
        print("   Start with: cd integrations && python api_endpoints.py")
        print("   Access at: http://localhost:8000/")
        
    else:
        print("\n❌ Dashboard cleanup verification failed!")
        print("   Please check the failed items above and fix them.")

if __name__ == "__main__":
    main()
