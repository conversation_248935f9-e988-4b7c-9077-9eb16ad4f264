"""
Instagram Messaging API Integration
Handles authentication and messaging for Instagram Business/Creator accounts
Uses Unipile API for unified Instagram messaging
Focus on comment replies and story interactions due to Instagram's messaging limitations
"""

import json
import time
from typing import Dict, List
import logging
from datetime import datetime
import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from unipile_api import UnipileAPI

class InstagramMessaging:
    def __init__(self, config_path: str = None):
        """Initialize Instagram API client with Unipile"""
        # Determine config path based on current working directory
        if config_path is None:
            import os
            current_dir = os.getcwd()
            if current_dir.endswith('integrations'):
                # Running from integrations directory
                config_path = "instagram_integration/config.json"
            else:
                # Running from root directory
                config_path = "integrations/instagram_integration/config.json"

        self.config_path = config_path

        # Setup logging first
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

        # Load config after logger is initialized
        self.config = self._load_config()

        # Unipile API setup
        self.unipile_client = None
        try:
            # Get API key from config or use default
            unipile_config = self.config.get("unipile", {})
            api_key = unipile_config.get("api_key", "bs8WGcCp./sF0oPlTIohHPT6hI+hsyO2HFyGzKIKdfC36gAvXTk0=")
            base_url = unipile_config.get("api_url", "https://api8.unipile.com:13814")

            if not api_key:
                api_key = "bs8WGcCp./sF0oPlTIohHPT6hI+hsyO2HFyGzKIKdfC36gAvXTk0="
                self.logger.warning("No API key found in config, using default")

            self.unipile_client = UnipileAPI(api_key=api_key, base_url=base_url)
            self.logger.info("Unipile API client initialized for Instagram")
        except Exception as e:
            self.logger.error(f"Failed to initialize Unipile API: {e}")
            raise Exception("Unipile API is required for Instagram integration")

        # Rate limiting for Unipile API
        self.last_request_time = 0
        self.min_request_interval = 1 / self.config.get("rate_limit", {}).get("messages_per_second", 5)

        # Account connection status
        self.connection_status = {"unipile": False}
    
    def _load_config(self) -> Dict:
        """Load configuration from JSON file"""
        try:
            with open(self.config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            if hasattr(self, 'logger'):
                self.logger.warning(f"Config file not found: {self.config_path}")
            return {}
        except json.JSONDecodeError:
            if hasattr(self, 'logger'):
                self.logger.error(f"Invalid JSON in config file: {self.config_path}")
            return {}
    
    def _save_config(self):
        """Save configuration to JSON file"""
        try:
            with open(self.config_path, 'w') as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            if hasattr(self, 'logger'):
                self.logger.error(f"Error saving config: {e}")
    
    def _rate_limit(self):
        """Implement rate limiting for Unipile API calls"""
        current_time = time.time()
        time_since_last_request = current_time - self.last_request_time

        if time_since_last_request < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last_request
            time.sleep(sleep_time)

        self.last_request_time = time.time()

    def authenticate_account(self, account_id: str = None) -> Dict:
        """Authenticate Instagram account via Unipile"""
        if not self.unipile_client:
            return {"error": "Unipile client not available"}

        try:
            # Check if account is already connected
            accounts = self.unipile_client.get_accounts()

            # Handle error response
            if "error" in accounts:
                return {
                    "success": False,
                    "error": accounts["error"],
                    "message": "Failed to check connected accounts. Please verify your Unipile API key."
                }

            instagram_accounts = [acc for acc in accounts.get("items", [])
                                if acc.get("type", "").upper() == "INSTAGRAM"]

            if instagram_accounts:
                self.connection_status["unipile"] = True
                self.logger.info("Instagram account already connected via Unipile")
                return {
                    "success": True,
                    "message": "Instagram account connected",
                    "accounts": instagram_accounts
                }
            else:
                # Return authentication URL or instructions
                return {
                    "success": False,
                    "message": "No Instagram account connected. Please connect via Unipile dashboard or use the connect-account endpoint.",
                    "auth_required": True,
                    "unipile_dashboard": "https://app.unipile.com/dashboard"
                }
        except Exception as e:
            self.logger.error(f"Authentication error: {e}")
            return {"error": str(e)}

    def generate_qr_code(self) -> Dict:
        """Generate QR code for Instagram authentication (alternative method)"""
        # Instagram doesn't support QR code authentication like WhatsApp
        # This is a placeholder that explains the limitation
        self.logger.info("QR code generation requested for Instagram...")

        return {
            "success": False,
            "error": "QR code authentication not supported for Instagram",
            "message": "Instagram doesn't support QR code authentication. Please use username/password credentials instead.",
            "alternative_methods": [
                "Use your Instagram username and password",
                "Ensure you're using a Business or Creator account",
                "Check that your API key has proper permissions",
                "Try connecting via the Unipile dashboard if API connection fails"
            ]
        }

    def connect_account(self, username: str, password: str) -> Dict:
        """Connect Instagram account using credentials via Unipile"""
        if not self.unipile_client:
            return {"error": "Unipile client not available"}

        try:
            # First, try the enhanced connection method with additional parameters
            connect_data = {
                "provider": "INSTAGRAM",
                "username": username,
                "password": password,
                "country": "US",  # Add country code
                "sync_limit": {
                    "chats": 100,
                    "messages": 1000
                }
            }

            self.logger.info(f"Attempting to connect Instagram account: {username}")
            self.logger.info(f"Using enhanced connection data with country and sync limits")

            result = self.unipile_client._make_request("POST", "api/v1/accounts", connect_data)
            self.logger.info(f"Unipile connection response: {result}")

            # Check if this is a checkpoint response (status 202)
            if result.get("object") == "Checkpoint":
                checkpoint_type = result.get("checkpoint", {}).get("type", "Unknown")
                account_id = result.get("account_id")
                self.logger.info(f"Checkpoint required: {checkpoint_type} for account {account_id}")

                return {
                    "success": False,
                    "checkpoint_required": True,
                    "checkpoint_type": checkpoint_type,
                    "account_id": account_id,
                    "message": f"Instagram authentication requires {checkpoint_type} verification",
                    "instructions": f"Please solve the {checkpoint_type} checkpoint to complete authentication"
                }

            if "error" in result:
                error_msg = result.get("error", "Unknown error")
                self.logger.error(f"Unipile API error: {error_msg}")

                # Handle specific error types with HTML-only solutions
                if "401" in str(error_msg) or "Unauthorized" in str(error_msg):
                    return {
                        "success": False,
                        "error": "Invalid API key",
                        "message": "Your Unipile API key is invalid or has expired. Please check your API key.",
                        "html_only_solution": True,
                        "troubleshooting": {
                            "suggestions": [
                                "Verify your Unipile API key is correct",
                                "Check if your API key has expired",
                                "Ensure your API key has Instagram permissions",
                                "Contact Unipile support if the key should be valid",
                                "Try regenerating your API key in Unipile dashboard"
                            ],
                            "api_key_issues": [
                                "API key may be expired or invalid",
                                "API key may not have Instagram account creation permissions",
                                "API key may be for a different Unipile environment"
                            ]
                        }
                    }
                elif "403" in str(error_msg) or "Forbidden" in str(error_msg):
                    return {
                        "success": False,
                        "error": "Account creation not permitted",
                        "message": "Your API key doesn't have permission to create Instagram accounts.",
                        "html_only_solution": True,
                        "troubleshooting": {
                            "suggestions": [
                                "Contact Unipile support to enable account creation permissions",
                                "Upgrade your Unipile plan if needed",
                                "Use the Unipile dashboard to manually add accounts",
                                "Check if your API key has the correct permissions"
                            ]
                        }
                    }
                elif "checkpoint" in str(error_msg).lower():
                    return {
                        "success": False,
                        "checkpoint_required": True,
                        "message": "Instagram requires verification. Please check your Instagram app for a verification code.",
                        "account_id": result.get("account_id"),
                        "error": error_msg
                    }
                elif "invalid_parameters" in str(error_msg).lower():
                    # Try simplified connection
                    return self._try_simplified_connection(username, password)
                else:
                    # Provide comprehensive HTML-only troubleshooting
                    return {
                        "success": False,
                        "error": error_msg,
                        "message": "Instagram account connection failed.",
                        "html_only_solution": True,
                        "troubleshooting": {
                            "suggestions": [
                                "Check your Instagram username and password",
                                "Ensure you're using a Business or Creator account",
                                "Verify your API key is valid and has permissions",
                                "Try again in a few minutes (rate limiting)",
                                "Contact support if issues persist"
                            ],
                            "common_issues": [
                                "Invalid credentials",
                                "Personal account instead of Business/Creator",
                                "API key permissions insufficient",
                                "Rate limiting or temporary restrictions"
                            ]
                        }
                    }

            # Success case
            if result.get("id") or result.get("account_id"):
                account_id = result.get("id") or result.get("account_id")
                self.logger.info(f"Instagram account connected successfully: {account_id}")

                # Update connection status
                self.connection_status["unipile"] = True

                # Verify the account was actually added by checking accounts list
                self.logger.info("Verifying account connection...")
                verification_result = self._verify_account_connection(account_id)

                return {
                    "success": True,
                    "account_id": account_id,
                    "message": "Instagram account connected successfully",
                    "account_info": result,
                    "verification": verification_result
                }
            else:
                self.logger.warning(f"Unexpected response format from Unipile: {result}")
                # Try alternative methods
                return self._try_alternative_connection_methods(username, password)

        except Exception as e:
            self.logger.error(f"Error connecting Instagram account: {e}")
            # Try alternative methods as fallback
            return self._try_alternative_connection_methods(username, password)

    def _try_simplified_connection(self, username: str, password: str) -> Dict:
        """Try simplified connection with minimal parameters"""
        try:
            self.logger.info("Trying simplified connection method...")

            # Minimal connection data
            connect_data = {
                "provider": "INSTAGRAM",
                "username": username,
                "password": password
            }

            result = self.unipile_client._make_request("POST", "api/v1/accounts", connect_data)
            self.logger.info(f"Simplified connection response: {result}")

            if "error" not in result and (result.get("id") or result.get("account_id")):
                account_id = result.get("id") or result.get("account_id")
                self.connection_status["unipile"] = True

                return {
                    "success": True,
                    "account_id": account_id,
                    "message": "Instagram account connected successfully (simplified method)",
                    "account_info": result
                }
            else:
                return {
                    "success": False,
                    "error": result.get("error", "Simplified connection failed"),
                    "message": "Unable to connect using simplified method. Please check your credentials and account type.",
                    "suggestion": "Make sure you're using an Instagram Business or Creator account."
                }

        except Exception as e:
            self.logger.error(f"Simplified connection failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Simplified connection method failed."
            }

    def _try_alternative_connection_methods(self, username: str, password: str) -> Dict:
        """Try alternative connection methods when standard approach fails"""
        try:
            self.logger.info("Trying alternative connection methods...")

            # Method 1: Try with different user agent
            connect_data = {
                "provider": "INSTAGRAM",
                "username": username,
                "password": password,
                "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            }

            result = self.unipile_client._make_request("POST", "api/v1/accounts", connect_data)
            self.logger.info(f"Alternative method 1 response: {result}")

            if "error" not in result and (result.get("id") or result.get("account_id")):
                account_id = result.get("id") or result.get("account_id")
                self.connection_status["unipile"] = True

                return {
                    "success": True,
                    "account_id": account_id,
                    "message": "Instagram account connected successfully (alternative method)",
                    "account_info": result
                }

            # Method 2: Try QR code generation as fallback
            self.logger.info("Trying QR code method as fallback...")
            qr_result = self.generate_qr_code()

            if qr_result.get("success"):
                return {
                    "success": False,
                    "qr_code_available": True,
                    "qr_code": qr_result.get("qr_code"),
                    "account_id": qr_result.get("account_id"),
                    "message": "Credential connection failed, but QR code is available. Scan with Instagram mobile app.",
                    "method": "qr_code"
                }

            # If all methods fail, provide helpful guidance
            return {
                "success": False,
                "error": "All connection methods failed",
                "message": "Unable to connect Instagram account using available methods.",
                "troubleshooting": {
                    "suggestions": [
                        "Verify your Instagram username and password",
                        "Ensure you're using a Business or Creator account",
                        "Check if 2FA is enabled (may require additional verification)",
                        "Try again in a few minutes (rate limiting)",
                        "Contact support if the issue persists"
                    ],
                    "account_requirements": [
                        "Instagram Business or Creator account",
                        "Valid username/email and password",
                        "Account must not have restrictions"
                    ]
                },
                "html_only_solution": True
            }

        except Exception as e:
            self.logger.error(f"Alternative connection methods failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "All connection attempts failed. Please verify your account credentials and type.",
                "html_only_solution": True
            }

    def _verify_account_connection(self, expected_account_id: str) -> Dict:
        """Verify that the account was successfully connected by checking the accounts list"""
        try:
            # Wait a moment for the account to be processed
            import time
            time.sleep(2)

            # Force refresh the accounts cache
            accounts = self.unipile_client.get_accounts(force_refresh=True)

            if "error" in accounts:
                return {
                    "verified": False,
                    "error": "Could not verify account connection",
                    "message": "Please check the Unipile dashboard to confirm your account was added."
                }

            # Look for the account in the list
            instagram_accounts = [acc for acc in accounts.get("items", [])
                                if acc.get("type", "").upper() == "INSTAGRAM"]

            # Check if our account is in the list
            account_found = False
            for account in instagram_accounts:
                if (account.get("id") == expected_account_id or
                    account.get("account_id") == expected_account_id):
                    account_found = True
                    break

            if account_found:
                return {
                    "verified": True,
                    "message": f"Account successfully verified in Unipile dashboard",
                    "total_instagram_accounts": len(instagram_accounts)
                }
            else:
                return {
                    "verified": False,
                    "message": f"Account not found in Unipile dashboard. Total Instagram accounts: {len(instagram_accounts)}",
                    "suggestion": "The account may still be processing. Please check the Unipile dashboard in a few minutes."
                }

        except Exception as e:
            self.logger.error(f"Error verifying account connection: {e}")
            return {
                "verified": False,
                "error": str(e),
                "message": "Could not verify account connection due to an error."
            }

    def solve_checkpoint(self, account_id: str, code: str) -> Dict:
        """Solve Instagram checkpoint verification"""
        if not self.unipile_client:
            return {"error": "Unipile client not available"}

        try:
            checkpoint_data = {
                "code": code
            }

            self.logger.info(f"Solving checkpoint for account: {account_id}")
            result = self.unipile_client._make_request("POST", f"api/v1/accounts/{account_id}/checkpoint", checkpoint_data)

            if "error" in result:
                return {
                    "success": False,
                    "error": result.get("error", "Unknown error"),
                    "message": "Failed to solve checkpoint. Please check the verification code."
                }

            # Check if another checkpoint is required
            if result.get("object") == "Checkpoint":
                checkpoint_type = result.get("checkpoint", {}).get("type", "Unknown")
                return {
                    "success": False,
                    "checkpoint_required": True,
                    "checkpoint_type": checkpoint_type,
                    "account_id": account_id,
                    "message": f"Additional verification required: {checkpoint_type}"
                }

            # Success case
            if result.get("id") or result.get("account_id"):
                final_account_id = result.get("id") or result.get("account_id")
                self.logger.info(f"Checkpoint solved successfully for account: {final_account_id}")

                # Update connection status
                self.connection_status["unipile"] = True

                return {
                    "success": True,
                    "account_id": final_account_id,
                    "message": "Instagram account verified and connected successfully"
                }
            else:
                return {
                    "success": False,
                    "error": "Unexpected response format",
                    "message": "Checkpoint may have been solved but response format is unexpected.",
                    "raw_response": result
                }

        except Exception as e:
            self.logger.error(f"Error solving checkpoint: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "An error occurred while solving the checkpoint."
            }

    def send_message(self, recipient_id: str, text: str, **kwargs) -> Dict:
        """Send Instagram message via Unipile"""
        if not self.unipile_client:
            return {"error": "Unipile client not available"}

        self._rate_limit()

        try:
            result = self.unipile_client.send_instagram_message(recipient_id, text, **kwargs)
            if "error" not in result:
                self.logger.info(f"Message sent via Unipile to {recipient_id}")
                return {"success": True, "result": result, "method": "unipile"}
            else:
                self.logger.error(f"Unipile message failed: {result.get('error')}")
                return {"error": result.get('error')}
        except Exception as e:
            self.logger.error(f"Unipile error: {e}")
            return {"error": str(e)}

    def send_bulk_messages(self, recipients: List[str], message: str, delay: float = 3.0) -> List[Dict]:
        """Send message to multiple recipients via Unipile"""
        if not self.unipile_client:
            return [{"error": "Unipile client not available"}]

        results = []

        for recipient_id in recipients:
            result = self.send_message(recipient_id, message)
            results.append({
                "recipient_id": recipient_id,
                "result": result,
                "method": "unipile",
                "timestamp": datetime.now().isoformat()
            })

            if delay > 0:
                time.sleep(delay)

        return results

    def get_connection_status(self) -> Dict:
        """Get current connection status for Unipile"""
        status = {
            "unipile": {
                "available": bool(self.unipile_client),
                "connected": False,
                "accounts": []
            }
        }

        # Check Unipile connection
        if self.unipile_client:
            try:
                accounts = self.unipile_client.get_accounts()
                # Handle error response
                if "error" in accounts:
                    self.logger.error(f"Unipile API error: {accounts['error']}")
                    status["unipile"]["connected"] = False
                    status["unipile"]["accounts"] = []
                else:
                    # Use correct response format - Unipile returns "items" not "accounts"
                    instagram_accounts = [acc for acc in accounts.get("items", [])
                                        if acc.get("type", "").upper() == "INSTAGRAM"]
                    status["unipile"]["connected"] = len(instagram_accounts) > 0
                    status["unipile"]["accounts"] = instagram_accounts
                    self.connection_status["unipile"] = len(instagram_accounts) > 0
            except Exception as e:
                self.logger.error(f"Error checking Unipile status: {e}")
                status["unipile"]["connected"] = False
                status["unipile"]["accounts"] = []

        return status

    def get_account_info(self) -> Dict:
        """Get Instagram account information via Unipile"""
        if not self.unipile_client:
            return {"error": "Unipile client not available"}

        try:
            self.logger.info("Fetching accounts from Unipile API...")
            accounts = self.unipile_client.get_accounts()
            self.logger.info(f"Unipile accounts response: {accounts}")

            # Handle error response
            if "error" in accounts:
                self.logger.error(f"Unipile API error: {accounts['error']}")
                return {"error": accounts["error"]}

            # Use correct response format - Unipile returns "items" not "accounts"
            all_accounts = accounts.get("items", accounts.get("accounts", []))
            self.logger.info(f"Total accounts found: {len(all_accounts)}")

            instagram_accounts = [acc for acc in all_accounts
                                if acc.get("type", "").upper() == "INSTAGRAM"]

            self.logger.info(f"Instagram accounts found: {len(instagram_accounts)}")

            if instagram_accounts:
                # Return the first Instagram account info
                account = instagram_accounts[0]
                self.logger.info(f"Using Instagram account: {account.get('id', 'N/A')}")

                return {
                    "success": True,
                    "account": account,
                    "id": account.get("account_id") or account.get("id"),
                    "username": account.get("username", "N/A"),
                    "name": account.get("name", "N/A"),
                    "provider": "instagram",
                    "debug_info": {
                        "total_accounts": len(all_accounts),
                        "instagram_accounts": len(instagram_accounts),
                        "account_keys": list(account.keys()) if account else []
                    }
                }
            else:
                # Provide detailed debug information
                account_types = [acc.get("type", "Unknown") for acc in all_accounts]
                self.logger.warning(f"No Instagram accounts found. Account types: {account_types}")

                return {
                    "error": "No Instagram account connected via Unipile",
                    "debug_info": {
                        "total_accounts": len(all_accounts),
                        "account_types": account_types,
                        "all_accounts": all_accounts  # Include for debugging
                    }
                }
        except Exception as e:
            self.logger.error(f"Error getting account info: {e}")
            return {"error": str(e)}

    def send_media_message(self, recipient_id: str, media_url: str,
                          media_type: str = "image", caption: str = None) -> Dict:
        """Send media message (image/video) to Instagram user via Unipile"""
        if not self.unipile_client:
            return {"error": "Unipile client not available"}

        if media_type not in ["image", "video"]:
            return {"error": "Media type must be 'image' or 'video'"}

        self._rate_limit()

        try:
            # Use Unipile to send media message
            result = self.unipile_client.send_instagram_message(
                recipient_id,
                caption or "",
                media_url=media_url,
                media_type=media_type
            )

            if "error" not in result:
                self.logger.info(f"Media message sent via Unipile to {recipient_id}")
                return {"success": True, "result": result, "method": "unipile"}
            else:
                self.logger.error(f"Failed to send media message: {result}")
                return {"error": result.get('error')}
        except Exception as e:
            self.logger.error(f"Media message error: {e}")
            return {"error": str(e)}

    def get_conversations(self, limit: int = 25) -> Dict:
        """Get Instagram conversations via Unipile"""
        if not self.unipile_client:
            return {"error": "Unipile client not available"}

        try:
            # Use Unipile to get conversations
            result = self.unipile_client.get_conversations("instagram", limit=limit)
            return {"success": True, "data": result}
        except Exception as e:
            self.logger.error(f"Error getting conversations: {e}")
            return {"error": str(e)}

    def get_messages(self, conversation_id: str, limit: int = 25) -> Dict:
        """Get messages from a conversation via Unipile"""
        if not self.unipile_client:
            return {"error": "Unipile client not available"}

        try:
            # Use Unipile to get messages
            result = self.unipile_client.get_messages(conversation_id, limit=limit)
            return {"success": True, "data": result}
        except Exception as e:
            self.logger.error(f"Error getting messages: {e}")
            return {"error": str(e)}

    def create_media_post(self, image_url: str, caption: str = None) -> Dict:
        """Create Instagram media post via Unipile"""
        if not self.unipile_client:
            return {"error": "Unipile client not available"}

        try:
            # Use Unipile to create Instagram post
            result = self.unipile_client.create_instagram_post(
                media_url=image_url,
                caption=caption or ""
            )

            if "error" not in result:
                self.logger.info("Instagram post created successfully via Unipile")
                return {"success": True, "result": result, "method": "unipile"}
            else:
                self.logger.error(f"Failed to create Instagram post: {result}")
                return {"error": result.get('error')}
        except Exception as e:
            self.logger.error(f"Post creation error: {e}")
            return {"error": str(e)}

    def create_story(self, media_url: str, media_type: str = "image") -> Dict:
        """Create Instagram story via Unipile"""
        if not self.unipile_client:
            return {"error": "Unipile client not available"}

        if media_type not in ["image", "video"]:
            return {"error": "Media type must be 'image' or 'video'"}

        try:
            # Use Unipile to create Instagram story
            result = self.unipile_client.create_instagram_story(
                media_url=media_url,
                media_type=media_type
            )

            if "error" not in result:
                self.logger.info("Instagram story created successfully via Unipile")
                return {"success": True, "result": result, "method": "unipile"}
            else:
                self.logger.error(f"Failed to create Instagram story: {result}")
                return {"error": result.get('error')}
        except Exception as e:
            self.logger.error(f"Story creation error: {e}")
            return {"error": str(e)}

    def reply_to_comment(self, comment_id: str, reply_text: str) -> Dict:
        """Reply to a comment on Instagram post (recommended for engagement)"""
        if not self.unipile_client:
            return {"error": "Unipile client not available"}

        try:
            # Use Unipile to reply to comment
            result = self.unipile_client.reply_to_instagram_comment(comment_id, reply_text)

            if "error" not in result:
                self.logger.info(f"Comment reply sent successfully via Unipile to {comment_id}")
                return {"success": True, "result": result, "method": "unipile"}
            else:
                self.logger.error(f"Failed to reply to comment: {result}")
                return {"error": result.get('error')}
        except Exception as e:
            self.logger.error(f"Comment reply error: {e}")
            return {"error": str(e)}

    def get_post_comments(self, media_id: str, limit: int = 25) -> Dict:
        """Get comments on a specific Instagram post via Unipile"""
        if not self.unipile_client:
            return {"error": "Unipile client not available"}

        try:
            # Use Unipile to get post comments
            result = self.unipile_client.get_instagram_post_comments(media_id, limit=limit)
            return {"success": True, "data": result}
        except Exception as e:
            self.logger.error(f"Error getting post comments: {e}")
            return {"error": str(e)}

    def get_story_mentions(self, limit: int = 25) -> Dict:
        """Get story mentions for the Instagram account via Unipile"""
        if not self.unipile_client:
            return {"error": "Unipile client not available"}

        try:
            # Use Unipile to get story mentions
            result = self.unipile_client.get_instagram_story_mentions(limit=limit)
            return {"success": True, "data": result}
        except Exception as e:
            self.logger.error(f"Error getting story mentions: {e}")
            return {"error": str(e)}

    def respond_to_story_mention(self, story_id: str, response_text: str) -> Dict:
        """Respond to a story mention via Unipile"""
        if not self.unipile_client:
            return {"error": "Unipile client not available"}

        try:
            # Use Unipile to respond to story mention
            result = self.unipile_client.respond_to_instagram_story(story_id, response_text)

            if "error" not in result:
                self.logger.info(f"Story mention response sent successfully via Unipile")
                return {"success": True, "result": result, "method": "unipile"}
            else:
                self.logger.error(f"Failed to respond to story mention: {result}")
                return {"error": result.get('error')}
        except Exception as e:
            self.logger.error(f"Story mention response error: {e}")
            return {"error": str(e)}

    def get_hashtag_mentions(self, hashtag: str, limit: int = 25) -> Dict:
        """Get recent posts mentioning a specific hashtag via Unipile"""
        if not self.unipile_client:
            return {"error": "Unipile client not available"}

        try:
            # Use Unipile to search hashtag mentions
            result = self.unipile_client.search_instagram_hashtag(hashtag, limit=limit)
            return {"success": True, "data": result}
        except Exception as e:
            self.logger.error(f"Error getting hashtag mentions: {e}")
            return {"error": str(e)}

    def send_template_message(self, recipient_id: str, template_name: str,
                            **kwargs) -> Dict:
        """Send predefined template message via Unipile"""
        templates = self.config.get("message_templates", {})

        if template_name not in templates:
            return {"error": f"Template '{template_name}' not found"}

        message = templates[template_name].format(**kwargs)

        # Send via Unipile
        return self.send_message(recipient_id, message)

    def is_configured(self) -> bool:
        """Check if Instagram API is properly configured"""
        return bool(self.unipile_client)

    def update_config(self, unipile_api_key: str = None, **kwargs):
        """Update configuration"""
        if unipile_api_key:
            # Ensure unipile config section exists
            if "unipile" not in self.config:
                self.config["unipile"] = {}

            self.config["unipile"]["api_key"] = unipile_api_key
            # Update the Unipile client with new API key
            if self.unipile_client:
                self.unipile_client.api_key = unipile_api_key
                self.unipile_client.headers["X-API-KEY"] = unipile_api_key
                self.logger.info("Unipile API key updated")

        # Update any other config values
        for key, value in kwargs.items():
            if key in self.config:
                self.config[key] = value

        self._save_config()
        self.logger.info("Instagram configuration updated")

    def update_api_key(self, api_key: str) -> Dict:
        """Update Unipile API key and test connection"""
        try:
            # Update the API key
            self.update_config(unipile_api_key=api_key)

            # Test the new API key
            test_result = self.unipile_client.get_accounts()

            if "error" in test_result:
                return {
                    "success": False,
                    "error": test_result["error"],
                    "message": "API key updated but connection test failed. Please verify the API key is correct."
                }

            return {
                "success": True,
                "message": "API key updated and tested successfully",
                "accounts_found": len(test_result.get("items", []))
            }

        except Exception as e:
            self.logger.error(f"Error updating API key: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to update API key"
            }

# Alias for backward compatibility
InstagramAPI = InstagramMessaging

# Example usage
if __name__ == "__main__":
    # Initialize Instagram messaging with Unipile
    instagram = InstagramMessaging()

    # Check connection status
    status = instagram.get_connection_status()
    print(f"Connection status: {status}")

    # Check if configured
    if not instagram.is_configured():
        print("Instagram API not configured. Please ensure Unipile API is available.")
    else:
        # Example: Get account info
        account_info = instagram.get_account_info()
        print(f"Account info: {account_info}")

        # Example: Test authentication
        auth_result = instagram.authenticate_account()
        print(f"Authentication result: {auth_result}")

        # Example: Send a test message via Unipile (replace with actual user ID)
        # result = instagram.send_message("USER_ID", "Hello from Instagram via Unipile!")
        # print(f"Message result: {result}")

        # Example: Reply to comment (recommended approach)
        # reply_result = instagram.reply_to_comment("COMMENT_ID", "Thanks for your comment! 😊")
        # print(f"Comment reply result: {reply_result}")
