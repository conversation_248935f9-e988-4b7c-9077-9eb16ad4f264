"""
TikTok Messaging API Integration
Handles authentication and messaging for TikTok Business accounts
Uses Unipile API as primary method with TikTok API for content management
Focus on comment replies and interaction messaging due to TikTok's messaging limitations
"""

import requests
import json
import time
from typing import Dict, List, Optional, Union
import logging
from datetime import datetime
import hashlib
import hmac
import base64
import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from unipile_api import UnipileAPI

class TikTokMessaging:
    def __init__(self, config_path: str = "integrations/tiktok_integration/config.json", use_unipile: bool = True):
        """Initialize TikTok API client with Unipile and TikTok API support"""
        self.config_path = config_path
        self.config = self._load_config()
        self.use_unipile = use_unipile

        # Setup logging first
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

        # Unipile API setup (primary method for messaging)
        self.unipile_client = None
        if use_unipile:
            try:
                # Get Unipile configuration
                unipile_config = self.config.get("unipile", {})
                api_key = unipile_config.get("api_key", "bs8WGcCp./sF0oPlTIohHPT6hI+hsyO2HFyGzKIKdfC36gAvXTk0=")
                base_url = unipile_config.get("api_url", "https://api8.unipile.com:13814")

                self.unipile_client = UnipileAPI(api_key=api_key, base_url=base_url)
                self.logger.info(f"Unipile API client initialized for TikTok with key: {api_key[:20]}...")
            except Exception as e:
                self.logger.warning(f"Failed to initialize Unipile API: {e}")
                self.use_unipile = False

        # TikTok API setup (for content management and comments)
        self.client_key = self.config.get("client_key")
        self.client_secret = self.config.get("client_secret")
        self.access_token = self.config.get("access_token")
        self.refresh_token = self.config.get("refresh_token")
        self.open_id = self.config.get("open_id")
        self.api_version = self.config.get("api_version", "v1.3")
        self.base_url = f"{self.config.get('base_url', 'https://open-api.tiktok.com')}/{self.api_version}"

        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 1 / self.config.get("rate_limit", {}).get("requests_per_second", 10)

        # Account connection status
        self.connection_status = {
            "unipile": False,
            "tiktok_api": False
        }
    
    def _load_config(self) -> Dict:
        """Load configuration from JSON file"""
        try:
            with open(self.config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            # Logger might not be initialized yet, so use print for early errors
            if hasattr(self, 'logger'):
                self.logger.warning(f"Config file not found: {self.config_path}")
            else:
                print(f"Warning: Config file not found: {self.config_path}")
            return {}
        except json.JSONDecodeError:
            if hasattr(self, 'logger'):
                self.logger.error(f"Invalid JSON in config file: {self.config_path}")
            else:
                print(f"Error: Invalid JSON in config file: {self.config_path}")
            return {}
    
    def _save_config(self):
        """Save configuration to JSON file"""
        try:
            with open(self.config_path, 'w') as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            self.logger.error(f"Error saving config: {e}")
    
    def _rate_limit(self):
        """Implement rate limiting"""
        current_time = time.time()
        time_since_last_request = current_time - self.last_request_time
        
        if time_since_last_request < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last_request
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def _make_request(self, method: str, endpoint: str, data: Dict = None, 
                     params: Dict = None, headers: Dict = None) -> Dict:
        """Make HTTP request with error handling"""
        if not self.access_token:
            return {"error": "Access token not configured"}
        
        self._rate_limit()
        
        url = f"{self.base_url}/{endpoint}"
        
        # Default headers
        default_headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }
        
        if headers:
            default_headers.update(headers)
        
        try:
            if method.upper() == "GET":
                response = requests.get(url, headers=default_headers, params=params)
            elif method.upper() == "POST":
                response = requests.post(url, headers=default_headers, json=data, params=params)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Request failed: {e}")
            return {"error": str(e)}

    def authenticate_account(self, force_new: bool = False) -> Dict:
        """Authenticate TikTok account via Unipile"""
        if not self.unipile_client:
            return {"error": "Unipile client not available"}

        try:
            # Check if account is already connected
            accounts = self.unipile_client.get_accounts()

            if "error" in accounts:
                return {"error": f"Failed to get accounts: {accounts['error']}"}

            # Look for TikTok accounts
            tiktok_accounts = []
            for acc in accounts.get("items", []):
                if acc.get("type", "").upper() == "TIKTOK":
                    tiktok_accounts.append(acc)

            if tiktok_accounts and not force_new:
                self.connection_status["unipile"] = True
                self.logger.info("TikTok account already connected via Unipile")
                return {
                    "success": True,
                    "message": "TikTok account connected",
                    "accounts": tiktok_accounts
                }
            else:
                # Return authentication URL or instructions
                return {
                    "success": False,
                    "message": "No TikTok account connected. Please connect via Unipile dashboard.",
                    "auth_required": True,
                    "dashboard_url": "https://dashboard.unipile.com"
                }
        except Exception as e:
            self.logger.error(f"Authentication error: {e}")
            return {"error": str(e)}

    def send_comment_message(self, recipient_id: str, text: str, **kwargs) -> Dict:
        """Send TikTok message via Unipile (primary) or comment interaction (fallback)"""
        # Try Unipile first
        if self.use_unipile and self.unipile_client:
            try:
                result = self.unipile_client.send_tiktok_message(recipient_id, text, **kwargs)
                if "error" not in result:
                    self.logger.info(f"Message sent via Unipile to {recipient_id}")
                    return {"success": True, "result": result, "method": "unipile"}
                else:
                    self.logger.warning(f"Unipile failed: {result.get('error')}, using comment interaction")
            except Exception as e:
                self.logger.warning(f"Unipile error: {e}, using comment interaction")

        # Fallback to comment-based interaction
        return self.send_engagement_message(recipient_id, "follow_up")

    def send_bulk_comment_messages(self, recipients: List[str], message: str, delay: float = 3.0) -> List[Dict]:
        """Send message to multiple recipients via Unipile (primary) or comment interactions (fallback)"""
        results = []

        # Try Unipile first for bulk messaging
        if self.use_unipile and self.unipile_client:
            try:
                # Unipile bulk messaging
                for recipient_id in recipients:
                    result = self.send_comment_message(recipient_id, message)
                    results.append({
                        "recipient_id": recipient_id,
                        "result": result,
                        "method": "unipile" if "error" not in result else "comment_interaction",
                        "timestamp": datetime.now().isoformat()
                    })

                    if delay > 0:
                        time.sleep(delay)

                return results
            except Exception as e:
                self.logger.warning(f"Unipile bulk messaging error: {e}, using comment interactions")

        # Fallback to comment-based interactions
        for recipient_id in recipients:
            result = self.send_engagement_message(recipient_id, "follow_up")
            results.append({
                "recipient_id": recipient_id,
                "result": result,
                "method": "comment_interaction",
                "timestamp": datetime.now().isoformat()
            })

            if delay > 0:
                time.sleep(delay)

        return results

    def get_connection_status(self) -> Dict:
        """Get current connection status for both Unipile and TikTok API"""
        status = {
            "unipile": {
                "available": bool(self.unipile_client),
                "connected": False,
                "accounts": []
            },
            "tiktok_api": {
                "available": bool(self.access_token and self.client_key),
                "connected": False,
                "open_id": self.open_id
            }
        }

        # Check Unipile connection
        if self.unipile_client:
            try:
                accounts = self.unipile_client.get_accounts()
                if "error" not in accounts:
                    tiktok_accounts = [acc for acc in accounts.get("items", [])
                                      if acc.get("type", "").upper() == "TIKTOK"]
                    status["unipile"]["connected"] = len(tiktok_accounts) > 0
                    status["unipile"]["accounts"] = tiktok_accounts
                else:
                    self.logger.error(f"Error getting Unipile accounts: {accounts['error']}")
            except Exception as e:
                self.logger.error(f"Error checking Unipile status: {e}")

        # Check TikTok API connection
        if self.access_token and self.client_key:
            try:
                user_info = self.get_user_info()
                status["tiktok_api"]["connected"] = "error" not in user_info
            except Exception as e:
                self.logger.error(f"Error checking TikTok API status: {e}")

        return status

    def get_user_info(self, fields: List[str] = None) -> Dict:
        """Get user information"""
        if not fields:
            fields = ["open_id", "union_id", "avatar_url", "display_name", "follower_count", "following_count", "likes_count", "video_count"]
        
        params = {
            "fields": ",".join(fields)
        }
        
        return self._make_request("GET", "user/info/", params=params)
    
    def upload_video(self, video_path: str, description: str = "", 
                    privacy_level: str = None, disable_duet: bool = False,
                    disable_comment: bool = False, disable_stitch: bool = False) -> Dict:
        """Upload video to TikTok"""
        if not privacy_level:
            privacy_level = self.config.get("video_settings", {}).get("privacy_level", "PUBLIC_TO_EVERYONE")
        
        # First, get upload URL
        upload_init_data = {
            "source_info": {
                "source": "FILE_UPLOAD",
                "video_size": self._get_file_size(video_path),
                "chunk_size": 10485760,  # 10MB chunks
                "total_chunk_count": 1
            }
        }
        
        init_result = self._make_request("POST", "video/init/", upload_init_data)
        
        if "error" in init_result:
            return init_result
        
        # Upload the video file
        upload_url = init_result.get("data", {}).get("upload_url")
        if not upload_url:
            return {"error": "Failed to get upload URL"}
        
        # Upload video file
        with open(video_path, 'rb') as video_file:
            files = {'video': video_file}
            upload_response = requests.post(upload_url, files=files)
        
        if upload_response.status_code != 200:
            return {"error": f"Video upload failed: {upload_response.text}"}
        
        # Publish the video
        publish_data = {
            "post_info": {
                "title": description,
                "privacy_level": privacy_level,
                "disable_duet": disable_duet,
                "disable_comment": disable_comment,
                "disable_stitch": disable_stitch,
                "video_cover_timestamp_ms": 1000
            },
            "source_info": {
                "source": "FILE_UPLOAD",
                "video_url": upload_url
            }
        }
        
        result = self._make_request("POST", "video/publish/", publish_data)
        
        if "error" not in result:
            self.logger.info("Video uploaded successfully to TikTok")
        else:
            self.logger.error(f"Failed to publish video: {result}")
        
        return result
    
    def get_video_list(self, cursor: int = 0, max_count: int = 20) -> Dict:
        """Get list of user's videos"""
        params = {
            "cursor": cursor,
            "max_count": max_count,
            "fields": "id,title,video_description,duration,cover_image_url,embed_link,like_count,comment_count,share_count,view_count"
        }
        
        return self._make_request("GET", "video/list/", params=params)
    
    def get_video_comments(self, video_id: str, cursor: int = 0, count: int = 50) -> Dict:
        """Get comments for a specific video"""
        params = {
            "video_id": video_id,
            "cursor": cursor,
            "count": count
        }
        
        return self._make_request("GET", "comment/list/", params=params)
    
    def reply_to_comment(self, video_id: str, comment_id: str, reply_text: str) -> Dict:
        """Reply to a comment on a video"""
        max_length = self.config.get("comment_settings", {}).get("max_reply_length", 150)
        
        if len(reply_text) > max_length:
            reply_text = reply_text[:max_length-3] + "..."
        
        data = {
            "video_id": video_id,
            "comment_id": comment_id,
            "text": reply_text
        }
        
        result = self._make_request("POST", "comment/reply/", data)
        
        if "error" not in result:
            self.logger.info(f"Reply posted successfully to comment {comment_id}")
        else:
            self.logger.error(f"Failed to reply to comment: {result}")
        
        return result

    def reply_to_comment_enhanced(self, video_id: str, comment_id: str, reply_text: str,
                                 mention_user: bool = True) -> Dict:
        """Enhanced comment reply with user mention and engagement tracking"""
        max_length = self.config.get("comment_settings", {}).get("max_reply_length", 150)

        # Add user mention if requested and space allows
        if mention_user and len(reply_text) < max_length - 10:
            # Note: TikTok mentions work with @username format
            reply_text = f"@user {reply_text}"

        if len(reply_text) > max_length:
            reply_text = reply_text[:max_length-3] + "..."

        data = {
            "video_id": video_id,
            "comment_id": comment_id,
            "text": reply_text
        }

        result = self._make_request("POST", "comment/reply/", data)

        if "error" not in result:
            self.logger.info(f"Enhanced reply posted successfully to comment {comment_id}")
            # Track engagement
            self._track_engagement("comment_reply", video_id, comment_id)
        else:
            self.logger.error(f"Failed to reply to comment: {result}")

        return result

    def get_video_comments_with_sentiment(self, video_id: str, cursor: int = 0, count: int = 50) -> Dict:
        """Get comments with basic sentiment analysis for better engagement"""
        params = {
            "video_id": video_id,
            "cursor": cursor,
            "count": count
        }

        result = self._make_request("GET", "comment/list/", params=params)

        if "error" not in result and "comments" in result.get("data", {}):
            # Add basic sentiment analysis
            comments = result["data"]["comments"]
            for comment in comments:
                comment["sentiment"] = self._analyze_comment_sentiment(comment.get("text", ""))
                comment["reply_priority"] = self._calculate_reply_priority(comment)

        return result

    def _analyze_comment_sentiment(self, text: str) -> str:
        """Basic sentiment analysis for comments"""
        positive_words = ["love", "amazing", "great", "awesome", "fantastic", "good", "nice", "cool"]
        negative_words = ["hate", "bad", "terrible", "awful", "worst", "horrible", "stupid"]

        text_lower = text.lower()
        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)

        if positive_count > negative_count:
            return "positive"
        elif negative_count > positive_count:
            return "negative"
        else:
            return "neutral"

    def _calculate_reply_priority(self, comment: Dict) -> int:
        """Calculate reply priority based on comment engagement and sentiment"""
        priority = 0

        # Higher priority for comments with more likes
        like_count = comment.get("like_count", 0)
        if like_count > 100:
            priority += 3
        elif like_count > 10:
            priority += 2
        elif like_count > 0:
            priority += 1

        # Higher priority for positive sentiment
        sentiment = comment.get("sentiment", "neutral")
        if sentiment == "positive":
            priority += 2
        elif sentiment == "negative":
            priority += 1  # Still important to address negative comments

        return priority

    def _track_engagement(self, action: str, video_id: str, target_id: str = None):
        """Track engagement actions for analytics"""
        engagement_data = {
            "action": action,
            "video_id": video_id,
            "target_id": target_id,
            "timestamp": datetime.now().isoformat()
        }

        # Store in config for basic tracking
        if "engagement_tracking" not in self.config:
            self.config["engagement_tracking"] = []

        self.config["engagement_tracking"].append(engagement_data)

        # Keep only last 100 entries
        if len(self.config["engagement_tracking"]) > 100:
            self.config["engagement_tracking"] = self.config["engagement_tracking"][-100:]

        self._save_config()

    def get_video_analytics(self, video_ids: List[str], fields: List[str] = None) -> Dict:
        """Get analytics for videos"""
        if not fields:
            fields = ["video_id", "like_count", "comment_count", "share_count", "view_count", "profile_view_count"]
        
        data = {
            "video_ids": video_ids,
            "fields": fields
        }
        
        return self._make_request("POST", "video/data/", data)
    
    def search_videos(self, keyword: str, count: int = 20, cursor: int = 0) -> Dict:
        """Search for videos by keyword"""
        params = {
            "keyword": keyword,
            "count": count,
            "cursor": cursor,
            "search_id": f"search_{int(time.time())}"
        }
        
        return self._make_request("GET", "research/video/query/", params=params)
    
    def get_hashtag_videos(self, hashtag_name: str, count: int = 20, cursor: int = 0) -> Dict:
        """Get videos for a specific hashtag"""
        params = {
            "hashtag_name": hashtag_name,
            "count": count,
            "cursor": cursor
        }
        
        return self._make_request("GET", "research/hashtag/video/", params=params)
    
    def refresh_access_token(self) -> Dict:
        """Refresh access token using refresh token"""
        if not self.refresh_token or not self.client_key or not self.client_secret:
            return {"error": "Refresh token or client credentials not configured"}
        
        data = {
            "client_key": self.client_key,
            "client_secret": self.client_secret,
            "grant_type": "refresh_token",
            "refresh_token": self.refresh_token
        }
        
        # Use different endpoint for token refresh
        url = "https://open-api.tiktok.com/oauth/refresh_token/"
        
        try:
            response = requests.post(url, json=data)
            response.raise_for_status()
            result = response.json()
            
            if "access_token" in result:
                self.access_token = result["access_token"]
                self.config["access_token"] = self.access_token
                
                if "refresh_token" in result:
                    self.refresh_token = result["refresh_token"]
                    self.config["refresh_token"] = self.refresh_token
                
                self._save_config()
                self.logger.info("Access token refreshed successfully")
            
            return result
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Token refresh failed: {e}")
            return {"error": str(e)}
    
    def send_engagement_message(self, user_open_id: str, message_type: str = "follow_up") -> Dict:
        """
        Send engagement message (Note: TikTok has limited direct messaging)
        This is more of a conceptual implementation as TikTok doesn't have traditional DM API
        """
        templates = self.config.get("message_templates", {})
        
        if message_type not in templates:
            return {"error": f"Message template '{message_type}' not found"}
        
        # In a real implementation, this might involve:
        # 1. Creating a video response
        # 2. Commenting on user's content
        # 3. Using TikTok's limited messaging features
        
        message = templates[message_type]
        
        # For now, we'll log this as TikTok doesn't have direct messaging API
        self.logger.info(f"Engagement message prepared for user {user_open_id}: {message}")
        
        return {
            "success": True,
            "message": "Engagement message prepared (TikTok has limited direct messaging)",
            "user_id": user_open_id,
            "message_content": message
        }
    
    def bulk_comment_replies(self, video_comments: List[Dict], reply_template: str, 
                           delay: float = 2.0) -> List[Dict]:
        """Reply to multiple comments with delay"""
        results = []
        
        for comment in video_comments:
            video_id = comment.get("video_id")
            comment_id = comment.get("comment_id")
            
            if video_id and comment_id:
                result = self.reply_to_comment(video_id, comment_id, reply_template)
                results.append({
                    "video_id": video_id,
                    "comment_id": comment_id,
                    "result": result,
                    "timestamp": datetime.now().isoformat()
                })
                
                if delay > 0:
                    time.sleep(delay)
        
        return results
    
    def _get_file_size(self, file_path: str) -> int:
        """Get file size in bytes"""
        try:
            import os
            return os.path.getsize(file_path)
        except OSError:
            return 0
    
    def is_configured(self) -> bool:
        """Check if TikTok API is properly configured"""
        return bool(self.access_token and self.client_key)
    
    def update_config(self, client_key: str = None, client_secret: str = None,
                     access_token: str = None, refresh_token: str = None, open_id: str = None,
                     unipile_api_key: str = None):
        """Update configuration"""
        if client_key:
            self.config["client_key"] = client_key
            self.client_key = client_key

        if client_secret:
            self.config["client_secret"] = client_secret
            self.client_secret = client_secret

        if access_token:
            self.config["access_token"] = access_token
            self.access_token = access_token

        if refresh_token:
            self.config["refresh_token"] = refresh_token
            self.refresh_token = refresh_token

        if open_id:
            self.config["open_id"] = open_id
            self.open_id = open_id

        if unipile_api_key:
            if "unipile" not in self.config:
                self.config["unipile"] = {}
            self.config["unipile"]["api_key"] = unipile_api_key

            # Reinitialize Unipile client with new API key
            try:
                base_url = self.config.get("unipile", {}).get("api_url", "https://api8.unipile.com:13814")
                self.unipile_client = UnipileAPI(api_key=unipile_api_key, base_url=base_url)
                self.use_unipile = True
                self.logger.info("Unipile client reinitialized with new API key")
            except Exception as e:
                self.logger.error(f"Failed to reinitialize Unipile client: {e}")

        self._save_config()
        self.logger.info("TikTok configuration updated")

    def test_connection(self) -> Dict:
        """Test TikTok API and Unipile connections"""
        results = {
            "unipile": {"available": False, "connected": False, "error": None},
            "tiktok_api": {"available": False, "connected": False, "error": None}
        }

        # Test Unipile connection
        if self.unipile_client:
            try:
                accounts = self.unipile_client.get_accounts()
                if "error" not in accounts:
                    results["unipile"]["available"] = True
                    tiktok_accounts = [acc for acc in accounts.get("items", [])
                                      if acc.get("type", "").upper() == "TIKTOK"]
                    results["unipile"]["connected"] = len(tiktok_accounts) > 0
                    results["unipile"]["accounts"] = len(tiktok_accounts)
                else:
                    results["unipile"]["error"] = accounts["error"]
            except Exception as e:
                results["unipile"]["error"] = str(e)
        else:
            results["unipile"]["error"] = "Unipile client not initialized"

        # Test TikTok API connection
        if self.is_configured():
            try:
                user_info = self.get_user_info()
                if "error" not in user_info:
                    results["tiktok_api"]["available"] = True
                    results["tiktok_api"]["connected"] = True
                    results["tiktok_api"]["user_info"] = user_info
                else:
                    results["tiktok_api"]["available"] = True
                    results["tiktok_api"]["error"] = user_info["error"]
            except Exception as e:
                results["tiktok_api"]["error"] = str(e)
        else:
            results["tiktok_api"]["error"] = "TikTok API not configured"

        return results

    def get_oauth_url(self, redirect_uri: str = None, scopes: List[str] = None) -> str:
        """Generate OAuth URL for TikTok authentication"""
        if not self.client_key:
            self.logger.error("Client key not configured")
            return None
            
        if not redirect_uri:
            redirect_uri = self.config.get("redirect_uri", "http://localhost:5000/api/tiktok/callback")
            
        if not scopes:
            scopes = ["user.info.basic", "video.list", "video.upload", "video.publish", "comment.list", "comment.post"]
            
        scope = ",".join(scopes)
        
        auth_url = f"https://www.tiktok.com/auth/authorize/?client_key={self.client_key}&scope={scope}&response_type=code&redirect_uri={redirect_uri}"
        
        return auth_url
    
    def handle_oauth_callback(self, code: str, redirect_uri: str = None) -> Dict:
        """Handle OAuth callback and get access token"""
        if not redirect_uri:
            redirect_uri = self.config.get("redirect_uri", "http://localhost:5000/api/tiktok/callback")
            
        if not self.client_key or not self.client_secret:
            return {"error": "Client key or secret not configured"}
            
        data = {
            "client_key": self.client_key,
            "client_secret": self.client_secret,
            "code": code,
            "grant_type": "authorization_code",
            "redirect_uri": redirect_uri
        }
        
        url = "https://open-api.tiktok.com/oauth/access_token/"
        
        try:
            response = requests.post(url, json=data)
            response.raise_for_status()
            result = response.json()
            
            if "data" in result and "access_token" in result["data"]:
                # Extract data from nested structure
                token_data = result["data"]
                
                # Update instance and config
                self.access_token = token_data["access_token"]
                self.refresh_token = token_data["refresh_token"]
                self.open_id = token_data["open_id"]
                
                # Update config
                self.config["access_token"] = self.access_token
                self.config["refresh_token"] = self.refresh_token
                self.config["open_id"] = self.open_id
                self._save_config()
                
                self.logger.info("TikTok authentication successful")
                
                return {
                    "success": True,
                    "message": "Authentication successful",
                    "data": token_data
                }
            else:
                self.logger.error(f"Authentication failed: {result}")
                return {"error": "Authentication failed", "details": result}
                
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Authentication request failed: {e}")
            return {"error": str(e)}

    def search_videos(self, keyword: str, count: int = 20, cursor: int = 0) -> Dict:
        """Search for videos by keyword"""
        params = {
            "keyword": keyword,
            "count": count,
            "cursor": cursor,
            "search_id": f"search_{int(time.time())}"
        }
        
        return self._make_request("GET", "research/video/query/", params=params)
    
    def get_hashtag_videos(self, hashtag_name: str, count: int = 20, cursor: int = 0) -> Dict:
        """Get videos for a specific hashtag"""
        params = {
            "hashtag_name": hashtag_name,
            "count": count,
            "cursor": cursor
        }
        
        return self._make_request("GET", "research/hashtag/video/", params=params)
    
    def refresh_access_token(self) -> Dict:
        """Refresh access token using refresh token"""
        if not self.refresh_token or not self.client_key or not self.client_secret:
            return {"error": "Refresh token or client credentials not configured"}
        
        data = {
            "client_key": self.client_key,
            "client_secret": self.client_secret,
            "grant_type": "refresh_token",
            "refresh_token": self.refresh_token
        }
        
        # Use different endpoint for token refresh
        url = "https://open-api.tiktok.com/oauth/refresh_token/"
        
        try:
            response = requests.post(url, json=data)
            response.raise_for_status()
            result = response.json()
            
            if "access_token" in result:
                self.access_token = result["access_token"]
                self.config["access_token"] = self.access_token
                
                if "refresh_token" in result:
                    self.refresh_token = result["refresh_token"]
                    self.config["refresh_token"] = self.refresh_token
                
                self._save_config()
                self.logger.info("Access token refreshed successfully")
            
            return result
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Token refresh failed: {e}")
            return {"error": str(e)}
    
    def send_engagement_message(self, user_open_id: str, message_type: str = "follow_up") -> Dict:
        """
        Send engagement message (Note: TikTok has limited direct messaging)
        This is more of a conceptual implementation as TikTok doesn't have traditional DM API
        """
        templates = self.config.get("message_templates", {})
        
        if message_type not in templates:
            return {"error": f"Message template '{message_type}' not found"}
        
        # In a real implementation, this might involve:
        # 1. Creating a video response
        # 2. Commenting on user's content
        # 3. Using TikTok's limited messaging features
        
        message = templates[message_type]
        
        # For now, we'll log this as TikTok doesn't have direct messaging API
        self.logger.info(f"Engagement message prepared for user {user_open_id}: {message}")
        
        return {
            "success": True,
            "message": "Engagement message prepared (TikTok has limited direct messaging)",
            "user_id": user_open_id,
            "message_content": message
        }
    
    def bulk_comment_replies(self, video_comments: List[Dict], reply_template: str, 
                           delay: float = 2.0) -> List[Dict]:
        """Reply to multiple comments with delay"""
        results = []
        
        for comment in video_comments:
            video_id = comment.get("video_id")
            comment_id = comment.get("comment_id")
            
            if video_id and comment_id:
                result = self.reply_to_comment(video_id, comment_id, reply_template)
                results.append({
                    "video_id": video_id,
                    "comment_id": comment_id,
                    "result": result,
                    "timestamp": datetime.now().isoformat()
                })
                
                if delay > 0:
                    time.sleep(delay)
        
        return results
    
    def _get_file_size(self, file_path: str) -> int:
        """Get file size in bytes"""
        try:
            import os
            return os.path.getsize(file_path)
        except OSError:
            return 0
    
    def is_configured(self) -> bool:
        """Check if TikTok API is properly configured"""
        return bool(self.access_token and self.client_key)
    
    def update_config(self, client_key: str = None, client_secret: str = None,
                     access_token: str = None, refresh_token: str = None, open_id: str = None):
        """Update configuration"""
        if client_key:
            self.config["client_key"] = client_key
            self.client_key = client_key
        
        if client_secret:
            self.config["client_secret"] = client_secret
            self.client_secret = client_secret
        
        if access_token:
            self.config["access_token"] = access_token
            self.access_token = access_token
        
        if refresh_token:
            self.config["refresh_token"] = refresh_token
            self.refresh_token = refresh_token
        
        if open_id:
            self.config["open_id"] = open_id
            self.open_id = open_id
        
        self._save_config()
        self.logger.info("TikTok configuration updated")

# Alias for backward compatibility
TikTokAPI = TikTokMessaging

# Example usage
if __name__ == "__main__":
    # Test with Unipile (primary)
    tiktok = TikTokMessaging(use_unipile=True)

    # Check connection status
    status = tiktok.get_connection_status()
    print(f"Connection status: {status}")

    # Check if configured
    if not tiktok.is_configured():
        print("TikTok API not configured. Please update config.json with your credentials.")
    else:
        # Example: Get user info
        user_info = tiktok.get_user_info()
        print(f"User info: {user_info}")

        # Example: Test authentication
        auth_result = tiktok.authenticate_account()
        print(f"Authentication result: {auth_result}")

        # Example: Get video list with enhanced comments
        videos = tiktok.get_video_list()
        print(f"Videos: {videos}")

        # Example: Send comment message via Unipile (replace with actual user ID)
        # result = tiktok.send_comment_message("USER_ID", "Thanks for engaging with our content!")
        # print(f"Message result: {result}")
