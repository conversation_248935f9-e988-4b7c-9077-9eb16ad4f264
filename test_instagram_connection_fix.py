#!/usr/bin/env python3
"""
Test the fixed Instagram connection functionality
"""

import sys
import os
import json

# Add integrations to path
sys.path.append('integrations')

def test_connection_fix():
    """Test the Instagram connection fix"""
    print("🧪 Testing Instagram Connection Fix")
    print("=" * 50)
    
    try:
        # Import Instagram API
        from instagram_integration.instagram_api import InstagramMessaging
        
        print("✅ Instagram API imported successfully")
        
        # Initialize
        instagram = InstagramMessaging()
        print("✅ Instagram API initialized")
        
        # Check Unipile client
        if not instagram.unipile_client:
            print("❌ Unipile client not available")
            return False
        
        print(f"✅ Unipile client available")
        print(f"🔑 API key: {instagram.unipile_client.api_key[:10]}...")
        
        # Test 1: Check current status
        print("\n📊 Testing connection status...")
        status = instagram.get_connection_status()
        print(f"Status: {json.dumps(status, indent=2)}")
        
        # Test 2: Check account info
        print("\n👤 Testing account info...")
        account_info = instagram.get_account_info()
        print(f"Account info: {json.dumps(account_info, indent=2)}")
        
        # Test 3: Direct API call
        print("\n🌐 Testing direct Unipile API call...")
        accounts = instagram.unipile_client.get_accounts()
        print(f"Direct API response: {json.dumps(accounts, indent=2)}")
        
        # Check for Instagram accounts
        all_accounts = accounts.get("items", accounts.get("accounts", []))
        instagram_accounts = [acc for acc in all_accounts if acc.get("type") == "INSTAGRAM"]
        
        print(f"\n📈 Summary:")
        print(f"Total accounts: {len(all_accounts)}")
        print(f"Instagram accounts: {len(instagram_accounts)}")
        
        if instagram_accounts:
            print("\n✅ Instagram accounts found:")
            for i, acc in enumerate(instagram_accounts):
                print(f"  Account {i+1}:")
                print(f"    ID: {acc.get('id', 'N/A')}")
                print(f"    Username: {acc.get('username', 'N/A')}")
                print(f"    Type: {acc.get('type', 'N/A')}")
                
                # Check if this is the expected account
                if acc.get('id') == "qGtnQgtJQ7SfWNHseeFpDQ":
                    print("    🎯 This is your previously connected account!")
        else:
            print("\n❌ No Instagram accounts found")
            print("💡 This means you need to reconnect your account")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_connection_method():
    """Test the connect_account method (without actual credentials)"""
    print("\n🔗 Testing Connection Method")
    print("=" * 30)
    
    try:
        from instagram_integration.instagram_api import InstagramMessaging
        
        instagram = InstagramMessaging()
        
        # Check if the method exists and is callable
        if hasattr(instagram, 'connect_account'):
            print("✅ connect_account method exists")
        else:
            print("❌ connect_account method missing")
            return False
        
        # Check if the solve_checkpoint method exists
        if hasattr(instagram, 'solve_checkpoint'):
            print("✅ solve_checkpoint method exists")
        else:
            print("❌ solve_checkpoint method missing")
            return False
        
        print("✅ All required methods are available")
        
        # Test method signature (without calling it)
        import inspect
        connect_sig = inspect.signature(instagram.connect_account)
        checkpoint_sig = inspect.signature(instagram.solve_checkpoint)
        
        print(f"connect_account signature: {connect_sig}")
        print(f"solve_checkpoint signature: {checkpoint_sig}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing methods: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Instagram Connection Fix Test")
    print("🎯 This will test the fixes to Instagram account connection")
    print()
    
    # Test 1: Connection status
    success1 = test_connection_fix()
    
    # Test 2: Method availability
    success2 = test_connection_method()
    
    print("\n" + "="*50)
    print("🏁 Test Results:")
    print(f"Connection test: {'✅ PASS' if success1 else '❌ FAIL'}")
    print(f"Method test: {'✅ PASS' if success2 else '❌ FAIL'}")
    
    if success1 and success2:
        print("\n🎉 All tests passed! The fixes are working correctly.")
        print("\n💡 Next steps:")
        print("1. Start the server")
        print("2. Open the Instagram HTML page")
        print("3. Try connecting your Instagram account again")
        print("4. Complete any verification steps that appear")
    else:
        print("\n⚠️  Some tests failed. Check the errors above.")
    
    print("\n🔗 Expected Account ID: qGtnQgtJQ7SfWNHseeFpDQ")
    print("If this appears in the test results, your account is already connected!")
