<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Instagram Business API Setup</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }
        h1 {
            background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .step {
            margin-bottom: 25px;
            padding: 20px;
            border-left: 4px solid #e6683c;
            background-color: #f9f9f9;
            border-radius: 0 10px 10px 0;
        }
        .step h3 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input[type="text"], input[type="password"], textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            box-sizing: border-box;
            transition: border-color 0.3s;
        }
        input:focus, textarea:focus {
            border-color: #e6683c;
            outline: none;
        }
        button {
            background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .code {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            border: 1px solid #e9ecef;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        a {
            color: #e6683c;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📷 Instagram Business API Setup</h1>
        
        <div class="warning">
            <strong>⚠️ Important:</strong> Instagram messaging requires a Business or Creator account.
            This integration uses Unipile API for unified Instagram messaging.
        </div>

        <div class="step">
            <h3>🚀 Step 1: Connect Your Instagram Account</h3>
            <div class="info">
                <strong>Connect via Unipile to enable Instagram messaging features</strong>
            </div>

            <div class="form-group">
                <label for="unipileApiKey">Unipile API Key:</label>
                <input type="password" id="unipileApiKey" name="unipileApiKey"
                       value="iGJsHDIR.sr6Pg30nB1cKUPC6eDY8tzVx+Opu+t+c6wQiKAEz2Ek="
                       placeholder="Your Unipile API key">
            </div>

            <button type="button" onclick="updateApiKey()">Update API Key</button>
            <button type="button" onclick="checkUnipileStatus()">Check Connection Status</button>

            <div id="unipileResult"></div>
            <div id="connectionStatus"></div>

            <!-- Instagram Account Connection -->
            <div class="form-group" style="margin-top: 20px;">
                <h4>📷 Add Instagram Account</h4>
                <div class="info">
                    <strong>Connect your Instagram Business/Creator account using your credentials:</strong>
                </div>

                <!-- Direct Credentials Connection -->
                <div style="margin: 15px 0; padding: 15px; border: 2px solid #e6683c; border-radius: 8px;">
                    <h5>🔑 Instagram Account Connection</h5>
                    <p>Connect using your Instagram username and password. Multiple connection methods available.</p>

                    <!-- Method 1: Credentials -->
                    <div class="form-group">
                        <label for="instagramUsername">Instagram Username/Email:</label>
                        <input type="text" id="instagramUsername" placeholder="your.<NAME_EMAIL>">
                    </div>
                    <div class="form-group">
                        <label for="instagramPassword">Instagram Password:</label>
                        <input type="password" id="instagramPassword" placeholder="Your Instagram password">
                    </div>
                    <button type="button" onclick="connectInstagramDirect()" style="background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%);">
                        🔐 Connect with Credentials
                    </button>

                    <!-- Note about Instagram limitations -->
                    <div style="margin-top: 15px; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px;">
                        <h6>ℹ️ Instagram Connection Notes</h6>
                        <p><small>
                            • Instagram requires Business or Creator accounts for API access<br>
                            • QR code authentication is not supported by Instagram<br>
                            • If API connection fails, manual connection via Unipile Dashboard is recommended
                        </small></p>
                    </div>

                    <div id="directConnectResult"></div>

                    <!-- Checkpoint Verification (hidden by default) -->
                    <div id="checkpointSection" style="display: none; margin-top: 15px; padding: 15px; border: 2px solid #ffc107; border-radius: 8px; background: #fff3cd;">
                        <h5>🔐 Verification Required</h5>
                        <p id="checkpointMessage">Instagram requires additional verification to complete the connection.</p>
                        <div class="form-group">
                            <label for="verificationCode">Verification Code:</label>
                            <input type="text" id="verificationCode" placeholder="Enter verification code">
                            <small id="checkpointInstructions">Check your email, SMS, or Instagram mobile app for the verification code.</small>
                        </div>
                        <button type="button" onclick="solveCheckpoint()" style="background: #ffc107; color: #212529;">
                            ✅ Verify Code
                        </button>
                        <div id="checkpointResult"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="step">
            <h3>📋 Step 2: Account Requirements</h3>
            <div class="info">
                <strong>Before connecting your Instagram account:</strong><br>
                1. Ensure you have an Instagram Business or Creator account<br>
                2. Sign up for Unipile API at <a href="https://unipile.com" target="_blank">unipile.com</a><br>
                3. Get your Unipile API key from the dashboard<br>
                4. Use the connection form above to add your Instagram account
            </div>
        </div>

        <div class="step">
            <h3>Step 2: Account Information & Troubleshooting</h3>
            <p>Once configured, you can get information about your Instagram account:</p>
            <button type="button" onclick="getAccountInfo()">Get Account Info</button>
            <button type="button" onclick="troubleshootConnection()" style="background: #ffc107; color: #212529;">🔧 Troubleshoot Connection</button>
            <div id="accountInfoResult"></div>
            <div id="troubleshootResult"></div>

            <div class="info" style="margin-top: 20px;">
                <strong>🔍 Common Issues & Solutions:</strong><br>
                <strong>1. "Account connected" but no info shows:</strong><br>
                • The account may still be processing in Unipile<br>
                • Wait 2-3 minutes and click "Check Connection Status"<br>
                • Verify in <a href="https://app.unipile.com/dashboard" target="_blank">Unipile Dashboard</a><br><br>

                <strong>2. Connection fails with credentials:</strong><br>
                • Ensure you're using a Business or Creator account<br>
                • Check if 2FA is enabled (may require verification)<br>
                • Try connecting via Unipile Dashboard instead<br><br>

                <strong>3. Account not appearing in dashboard:</strong><br>
                • Some API keys have limited account creation permissions<br>
                • Use the Unipile Dashboard to manually add accounts<br>
                • Contact Unipile support if issues persist<br>
            </div>
        </div>

        <div class="step">
            <h3>Step 7: Test Direct Messaging</h3>
            <div class="info">
                <strong>Important:</strong> You can only send messages to users who have initiated contact with your business account first.
            </div>
            
            <div class="form-group">
                <label for="testUserId">User Instagram ID:</label>
                <input type="text" id="testUserId" placeholder="Instagram user ID (IGSID)">
            </div>
            <div class="form-group">
                <label for="testMessage">Test Message:</label>
                <textarea id="testMessage" rows="3" placeholder="Hello! Thanks for reaching out to us on Instagram."></textarea>
            </div>
            <button type="button" onclick="sendTestMessage()">Send Test Message</button>
            <div id="testResult"></div>
        </div>

        <div class="step">
            <h3>Step 8: Bulk Messaging</h3>
            <div class="info">
                <strong>Send messages to multiple recipients using unified API (Unipile + Facebook Graph API fallback)</strong>
            </div>

            <div class="form-group">
                <label for="bulkRecipients">Recipient IDs (one per line):</label>
                <textarea id="bulkRecipients" rows="4" placeholder="user_id_1&#10;user_id_2&#10;user_id_3"></textarea>
            </div>
            <div class="form-group">
                <label for="bulkMessage">Bulk Message:</label>
                <textarea id="bulkMessage" rows="3" placeholder="Hello! This is a bulk message from our Instagram account."></textarea>
            </div>
            <div class="form-group">
                <label for="messageDelay">Delay between messages (seconds):</label>
                <input type="number" id="messageDelay" value="3" min="1" max="60">
            </div>
            <button type="button" onclick="sendBulkMessages()">Send Bulk Messages</button>
            <div id="bulkResult"></div>
        </div>

        <div class="step">
            <h3>Step 9: Content Publishing</h3>
            <p>You can also publish content to Instagram:</p>
            <div class="form-group">
                <label for="imageUrl">Image URL:</label>
                <input type="text" id="imageUrl" placeholder="https://example.com/image.jpg">
            </div>
            <div class="form-group">
                <label for="caption">Caption:</label>
                <textarea id="caption" rows="3" placeholder="Your Instagram post caption with #hashtags"></textarea>
            </div>
            <button type="button" onclick="createPost()">Create Post</button>
            <button type="button" onclick="createStory()">Create Story</button>
            <div id="contentResult"></div>
        </div>

        <div class="step">
            <h3>Step 10: Comment Replies & Story Interactions</h3>
            <div class="info">
                <strong>💡 Recommended: Use comment replies and story interactions for better engagement without DM restrictions</strong>
            </div>

            <h4>Comment Replies</h4>
            <div class="form-group">
                <label for="commentId">Comment ID:</label>
                <input type="text" id="commentId" placeholder="Comment ID to reply to">
            </div>
            <div class="form-group">
                <label for="commentReply">Reply Text:</label>
                <textarea id="commentReply" rows="2" placeholder="Thanks for your comment! 😊"></textarea>
            </div>
            <button type="button" onclick="replyToComment()">Reply to Comment</button>
            <div id="commentReplyResult"></div>

            <h4>Get Post Comments</h4>
            <div class="form-group">
                <label for="postId">Post/Media ID:</label>
                <input type="text" id="postId" placeholder="Instagram post/media ID">
            </div>
            <button type="button" onclick="getPostComments()">Get Comments</button>
            <div id="commentsResult"></div>
        </div>

        <div class="step">
            <h3>Step 11: Getting User IDs</h3>
            <p>To send messages, you need Instagram-scoped user IDs (IGSID):</p>
            <ul>
                <li><strong>Webhook:</strong> Set up webhook to receive message events</li>
                <li><strong>Conversations API:</strong> Get existing conversations</li>
                <li><strong>User interaction:</strong> Users must message your business first</li>
            </ul>
            <button type="button" onclick="getConversations()">Get Conversations</button>
            <div id="conversationsResult"></div>
        </div>

        <div class="step">
            <h3>Step 12: Instagram Messaging Limitations</h3>
            <div class="warning">
                <strong>⚠️ Instagram Messaging Restrictions:</strong>
                <ul>
                    <li><strong>Direct Messages:</strong> Limited to users who message your business first</li>
                    <li><strong>24-hour rule:</strong> Can only message users who contacted you within 24 hours</li>
                    <li><strong>Business verification:</strong> Some features require Meta business verification</li>
                </ul>
            </div>

            <div class="info">
                <strong>💡 Recommended Alternatives:</strong>
                <ul>
                    <li><strong>Comment Replies:</strong> Respond to comments on your posts</li>
                    <li><strong>Story Interactions:</strong> Engage with story mentions and reactions</li>
                    <li><strong>Content Publishing:</strong> Create posts and stories to engage audience</li>
                    <li><strong>Hashtag Monitoring:</strong> Track mentions and branded hashtags</li>
                </ul>
            </div>
        </div>

        <div class="step">
            <h3>Step 13: Rate Limits & Best Practices</h3>
            <ul>
                <li><strong>Rate Limits:</strong> 200 messages per minute, 1000 per hour</li>
                <li><strong>24-hour rule:</strong> Can only message users who contacted you within 24 hours</li>
                <li><strong>Content policy:</strong> Follow Instagram's community guidelines</li>
                <li><strong>Business use only:</strong> Don't spam or send unsolicited messages</li>
                <li><strong>Focus on engagement:</strong> Use comments and stories for broader reach</li>
            </ul>
        </div>
    </div>

    <script>
        // Auto-refresh connection status every 30 seconds
        setInterval(checkUnipileStatus, 30000);

        function updateApiKey() {
            const apiKey = document.getElementById('unipileApiKey').value;
            if (!apiKey) {
                document.getElementById('unipileResult').innerHTML =
                    '<div class="error">❌ Please enter your Unipile API key</div>';
                return;
            }

            document.getElementById('unipileResult').innerHTML = '<div class="info">🔄 Updating API key...</div>';

            fetch('/api/instagram/update-api-key', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    api_key: apiKey
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('unipileResult');
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ API key updated successfully!<br>
                            <small>Found ${data.accounts_found || 0} connected accounts</small>
                        </div>
                    `;
                    checkUnipileStatus();
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to update API key: ' + (data.detail || data.error || 'Unknown error') + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('unipileResult').innerHTML =
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }

        function checkUnipileStatus() {
            document.getElementById('connectionStatus').innerHTML = '<div class="info">🔄 Checking connection status...</div>';

            fetch('/api/instagram/connection-status')
            .then(response => response.json())
            .then(data => {
                const statusDiv = document.getElementById('connectionStatus');
                if (data.success) {
                    const status = data.status;

                    if (status.unipile.connected && status.unipile.accounts.length > 0) {
                        let html = '<div class="success"><strong>✅ Connected Instagram Accounts:</strong><br>';
                        status.unipile.accounts.forEach(account => {
                            html += `📷 Account ID: ${account.account_id || account.id}<br>`;
                            html += `👤 Username: @${account.username || 'N/A'}<br>`;
                            html += `📧 Name: ${account.name || 'N/A'}<br>`;
                            html += `🔗 Provider: ${account.provider || account.type || 'N/A'}<br><br>`;
                        });
                        html += '</div>';
                        statusDiv.innerHTML = html;
                    } else if (status.unipile.available) {
                        let debugInfo = '';
                        if (data.debug_info) {
                            debugInfo = `<br><small>Debug: API Key configured: ${data.debug_info.api_key_configured}, Client available: ${data.debug_info.unipile_client_available}</small>`;
                        }
                        statusDiv.innerHTML = `
                            <div class="info">
                                <strong>📋 Unipile API Available</strong><br>
                                No Instagram accounts found. Use the form above to add an Instagram account,
                                then click "Check Connection Status" to refresh.${debugInfo}
                            </div>
                        `;
                    } else {
                        statusDiv.innerHTML = `
                            <div class="warning">
                                <strong>⚠️ Unipile API Not Available</strong><br>
                                Please update your API key using the form above.
                            </div>
                        `;
                    }
                } else {
                    statusDiv.innerHTML = '<div class="error">❌ Failed to get status: ' + (data.detail || data.error || 'Unknown error') + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('connectionStatus').innerHTML =
                    '<div class="error">❌ Status check error: ' + error.message + '</div>';
            });
        }

        // QR code generation removed - Instagram doesn't support QR code authentication

        function connectInstagramDirect() {
            const username = document.getElementById('instagramUsername').value;
            const password = document.getElementById('instagramPassword').value;
            const resultDiv = document.getElementById('directConnectResult');

            if (!username || !password) {
                resultDiv.innerHTML = '<div class="error">❌ Please enter both username and password</div>';
                return;
            }

            resultDiv.innerHTML = '<div class="info">🔄 Connecting Instagram account...</div>';

            fetch('/api/instagram/connect-account', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username: username,
                    password: password
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    let debugInfo = '';
                    if (data.debug_info) {
                        debugInfo = `<br><small>Username: ${data.debug_info.username || 'N/A'}</small>`;
                        if (data.debug_info.verification) {
                            const verification = data.debug_info.verification;
                            if (verification.verified) {
                                debugInfo += `<br><small>✅ Account verified in dashboard</small>`;
                            } else {
                                debugInfo += `<br><small>⚠️ ${verification.message}</small>`;
                            }
                        }
                    }
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ Instagram account connected successfully!<br>
                            Account ID: ${data.account_id || 'N/A'}${debugInfo}<br>
                            <small>Account should now be accessible for messaging.</small>
                        </div>
                    `;
                    // Clear the password field for security
                    document.getElementById('instagramPassword').value = '';
                    // Refresh status
                    setTimeout(checkUnipileStatus, 2000);
                } else if (data.checkpoint_required) {
                    // Handle checkpoint requirement
                    checkpointAccountId = data.account_id;
                    showCheckpointSection(data.checkpoint_type, data.message);
                    resultDiv.innerHTML = `
                        <div class="info">
                            🔐 Verification Required<br>
                            ${data.message}<br>
                            <small>Please complete the verification below.</small>
                        </div>
                    `;
                // QR code fallback removed - Instagram doesn't support QR codes
                } else if (data.html_only_solution || data.manual_connection_required) {
                    // HTML-only troubleshooting with manual connection guidance
                    let troubleshootingHtml = `
                        <div class="warning">
                    `;

                    // Special handling for API permission issues
                    if (data.api_permission_issue) {
                        troubleshootingHtml += `
                            🔑 API Permission Issue Detected<br>
                            ${data.message}<br><br>
                            <div style="background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;">
                                <strong>✅ Good News:</strong> Your API key is working for reading accounts!<br>
                                <strong>⚠️ Issue:</strong> It doesn't have permission to create new accounts.<br>
                                <strong>💡 Solution:</strong> Use the manual connection method below.
                            </div>
                        `;
                    } else {
                        troubleshootingHtml += `
                            ⚠️ API Connection Failed - Manual Connection Required<br>
                            ${data.message}<br><br>
                        `;
                    }

                    if (data.troubleshooting) {
                        const t = data.troubleshooting;

                        if (t.primary_issue) {
                            troubleshootingHtml += `<strong>🔍 Root Cause:</strong><br>${t.primary_issue}<br><br>`;
                        }

                        if (t.immediate_solution) {
                            troubleshootingHtml += `<strong>🚀 Quick Solution:</strong><br>${t.immediate_solution}<br><br>`;
                        }

                        if (t.step_by_step_manual_connection) {
                            troubleshootingHtml += `<strong>🔧 Step-by-Step Manual Connection:</strong><br>`;
                            t.step_by_step_manual_connection.forEach(step => {
                                troubleshootingHtml += `${step}<br>`;
                            });
                            troubleshootingHtml += `<br>`;
                        }

                        if (t.why_this_happens) {
                            troubleshootingHtml += `<strong>🤔 Why This Happens:</strong><br>`;
                            t.why_this_happens.forEach(reason => {
                                troubleshootingHtml += `• ${reason}<br>`;
                            });
                            troubleshootingHtml += `<br>`;
                        }

                        if (t.account_requirements) {
                            troubleshootingHtml += `<strong>📋 Instagram Account Requirements:</strong><br>`;
                            t.account_requirements.forEach(req => {
                                troubleshootingHtml += `• ${req}<br>`;
                            });
                            troubleshootingHtml += `<br>`;
                        }

                        if (t.immediate_solutions) {
                            troubleshootingHtml += `<strong>💡 Alternative Solutions:</strong><br>`;
                            t.immediate_solutions.forEach((solution, index) => {
                                troubleshootingHtml += `${index + 1}. ${solution}<br>`;
                            });
                            troubleshootingHtml += `<br>`;
                        }

                        if (t.api_limitations) {
                            troubleshootingHtml += `<strong>⚠️ API Limitations:</strong><br>`;
                            t.api_limitations.forEach(limitation => {
                                troubleshootingHtml += `• ${limitation}<br>`;
                            });
                        }
                    }

                    if (data.dashboard_url) {
                        troubleshootingHtml += `
                            <br><div style="text-align: center; margin: 15px 0;">
                                <a href="${data.dashboard_url}" target="_blank"
                                   style="background: #e6683c; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">
                                    🔗 Open Unipile Dashboard (Recommended)
                                </a>
                            </div>
                            <div style="text-align: center; font-size: 12px; color: #666;">
                                This will open in a new tab. After connecting, return here and refresh the page.
                            </div>
                        `;
                    }

                    troubleshootingHtml += `</div>`;

                    resultDiv.innerHTML = troubleshootingHtml;
                } else {
                    let errorHtml = '<div class="error">❌ Failed to connect: ' + (data.detail || data.error || data.message || 'Unknown error');

                    // Add suggestion if available
                    if (data.suggestion) {
                        errorHtml += '<br><br><strong>💡 Suggestion:</strong><br>' + data.suggestion;
                    }

                    // Alternative methods are now handled by the improved error handling above
                    errorHtml += '</div>';

                    resultDiv.innerHTML = errorHtml;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = '<div class="error">❌ Connection error: ' + error.message +
                    '<br><br><strong>💡 Try:</strong><br>1. Check if the server is running<br>2. Refresh the page and try again<br>3. Use manual connection via Unipile Dashboard if needed</div>';
            });
        }

        // Global variable to store checkpoint account ID
        let checkpointAccountId = null;

        function showCheckpointSection(checkpointType, message) {
            const checkpointSection = document.getElementById('checkpointSection');
            const checkpointMessage = document.getElementById('checkpointMessage');
            const checkpointInstructions = document.getElementById('checkpointInstructions');

            checkpointMessage.textContent = message;

            // Update instructions based on checkpoint type
            let instructions = '';
            switch(checkpointType) {
                case '2FA':
                    instructions = 'Enter the 6-digit code from your authenticator app.';
                    break;
                case 'OTP':
                    instructions = 'Check your email or SMS for the verification code.';
                    break;
                case 'IN_APP_VALIDATION':
                    instructions = 'Open your Instagram mobile app and approve the connection request.';
                    break;
                case 'PHONE_REGISTER':
                    instructions = 'Enter your phone number with country code, e.g., (+1)5551234567';
                    break;
                case 'CAPTCHA':
                    instructions = 'Complete the CAPTCHA verification.';
                    break;
                default:
                    instructions = 'Follow the verification instructions from Instagram.';
            }

            checkpointInstructions.textContent = instructions;
            checkpointSection.style.display = 'block';
        }

        function solveCheckpoint() {
            const code = document.getElementById('verificationCode').value;
            const resultDiv = document.getElementById('checkpointResult');

            if (!code) {
                resultDiv.innerHTML = '<div class="error">❌ Please enter the verification code</div>';
                return;
            }

            if (!checkpointAccountId) {
                resultDiv.innerHTML = '<div class="error">❌ No checkpoint session found. Please try connecting again.</div>';
                return;
            }

            resultDiv.innerHTML = '<div class="info">🔄 Verifying code...</div>';

            fetch('/api/instagram/solve-checkpoint', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    account_id: checkpointAccountId,
                    code: code
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ Verification successful!<br>
                            ${data.message}<br>
                            ${data.account_id ? `Account ID: ${data.account_id}` : ''}
                        </div>
                    `;
                    // Hide checkpoint section
                    setTimeout(() => {
                        document.getElementById('checkpointSection').style.display = 'none';
                        document.getElementById('verificationCode').value = '';
                        checkpointAccountId = null;
                    }, 2000);
                    // Refresh status
                    setTimeout(checkUnipileStatus, 3000);
                } else if (data.checkpoint_required) {
                    // Another checkpoint required
                    showCheckpointSection(data.checkpoint_type, data.message);
                    resultDiv.innerHTML = `
                        <div class="info">
                            🔐 Additional verification required<br>
                            ${data.message}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Verification failed: ' + (data.detail || data.error || data.message || 'Unknown error') + '</div>';
                }
            })
            .catch(error => {
                resultDiv.innerHTML = '<div class="error">❌ Verification error: ' + error.message + '</div>';
            });
        }

        function saveConfig() {
            const config = {
                unipile_api_key: document.getElementById('unipileApiKey').value
            };
            
            fetch('/api/instagram/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(config)
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('configResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Configuration saved successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Error saving configuration: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('configResult').innerHTML =
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }

        function troubleshootConnection() {
            document.getElementById('troubleshootResult').innerHTML = '<div class="info">🔧 Running comprehensive connection diagnostics...</div>';

            // Step 1: Check API key and connection status
            fetch('/api/instagram/connection-status')
            .then(response => response.json())
            .then(data => {
                let diagnostics = '<div class="info"><strong>🔍 Connection Diagnostics Report:</strong><br><br>';

                if (data.success && data.status) {
                    const status = data.status;
                    diagnostics += `<strong>📡 API Status:</strong><br>`;
                    diagnostics += `✅ Unipile API Available: ${status.unipile.available}<br>`;
                    diagnostics += `🔗 Connected: ${status.unipile.connected}<br>`;
                    diagnostics += `📊 Instagram Accounts Found: ${status.unipile.accounts.length}<br>`;

                    if (data.debug_info) {
                        diagnostics += `🔑 API Key Configured: ${data.debug_info.api_key_configured}<br>`;
                        diagnostics += `🛠️ Client Available: ${data.debug_info.unipile_client_available}<br>`;
                    }

                    diagnostics += `<br><strong>🔍 Account Analysis:</strong><br>`;
                    if (status.unipile.accounts.length === 0) {
                        diagnostics += `❌ <strong>Issue Identified:</strong> No Instagram accounts connected to Unipile<br><br>`;
                        diagnostics += `<strong>🛠️ Recommended Solutions (in order):</strong><br>`;
                        diagnostics += `1. <strong>Manual Dashboard Connection:</strong><br>`;
                        diagnostics += `   • Visit <a href="https://app.unipile.com/dashboard" target="_blank">Unipile Dashboard</a><br>`;
                        diagnostics += `   • Click "Add Account" → Select "Instagram"<br>`;
                        diagnostics += `   • Enter your credentials and complete verification<br>`;
                        diagnostics += `   • Return here and click "Check Connection Status"<br><br>`;

                        diagnostics += `2. <strong>API Key Permissions:</strong><br>`;
                        diagnostics += `   • Your API key may not have account creation permissions<br>`;
                        diagnostics += `   • Contact Unipile support to verify API key capabilities<br>`;
                        diagnostics += `   • Consider upgrading your Unipile plan if needed<br><br>`;

                        diagnostics += `3. <strong>Account Requirements:</strong><br>`;
                        diagnostics += `   • Ensure you're using Instagram Business/Creator account<br>`;
                        diagnostics += `   • Personal accounts may not work with business APIs<br>`;
                        diagnostics += `   • Convert to Business account in Instagram settings<br><br>`;

                        diagnostics += `4. <strong>Connection Timing:</strong><br>`;
                        diagnostics += `   • Account processing can take 2-5 minutes<br>`;
                        diagnostics += `   • Try waiting and refreshing connection status<br>`;
                        diagnostics += `   • Some accounts require manual verification steps<br>`;
                    } else {
                        diagnostics += `✅ <strong>Accounts Successfully Connected:</strong><br>`;
                        status.unipile.accounts.forEach((acc, index) => {
                            diagnostics += `<br><strong>Account ${index + 1}:</strong><br>`;
                            diagnostics += `• ID: ${acc.id || acc.account_id || 'N/A'}<br>`;
                            diagnostics += `• Username: @${acc.username || 'N/A'}<br>`;
                            diagnostics += `• Name: ${acc.name || 'N/A'}<br>`;
                            diagnostics += `• Type: ${acc.type || 'N/A'}<br>`;
                            diagnostics += `• Status: ${acc.status || 'Active'}<br>`;
                        });

                        diagnostics += `<br><strong>✅ Your Instagram integration is working correctly!</strong><br>`;
                        diagnostics += `You can now use the messaging features below.`;
                    }
                } else {
                    diagnostics += `❌ <strong>API Connection Failed</strong><br>`;
                    diagnostics += `Error: ${data.error || 'Unknown error'}<br><br>`;
                    diagnostics += `<strong>🛠️ Troubleshooting Steps:</strong><br>`;
                    diagnostics += `1. Check if the server is running<br>`;
                    diagnostics += `2. Verify your Unipile API key is correct<br>`;
                    diagnostics += `3. Check your internet connection<br>`;
                    diagnostics += `4. Try refreshing the page<br>`;
                }

                diagnostics += '<br><br><strong>🔗 Additional Resources:</strong><br>';
                diagnostics += `• <a href="https://app.unipile.com/dashboard" target="_blank">Unipile Dashboard</a><br>`;
                diagnostics += `• <a href="https://docs.unipile.com" target="_blank">Unipile Documentation</a><br>`;
                diagnostics += `• <a href="https://help.instagram.com/***************" target="_blank">Instagram Business Account Setup</a><br>`;
                diagnostics += '</div>';

                document.getElementById('troubleshootResult').innerHTML = diagnostics;
            })
            .catch(error => {
                document.getElementById('troubleshootResult').innerHTML =
                    '<div class="error">❌ Troubleshoot error: ' + error.message +
                    '<br><br><strong>This usually means:</strong><br>' +
                    '• The server is not running<br>' +
                    '• Network connectivity issues<br>' +
                    '• API endpoint is not accessible<br>' +
                    '<br>Please ensure the server is running and try again.</div>';
            });
        }

        function getAccountInfo() {
            document.getElementById('accountInfoResult').innerHTML = '<div class="info">🔄 Getting account information...</div>';

            fetch('/api/instagram/account-info')
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('accountInfoResult');
                if (data.success && data.data) {
                    const info = data.data;
                    resultDiv.innerHTML = `
                        <div class="success">
                            <strong>📷 Instagram Account Information:</strong><br>
                            👤 Username: @${info.username || 'N/A'}<br>
                            📝 Name: ${info.name || 'N/A'}<br>
                            🆔 Account ID: ${info.account_id || 'N/A'}<br>
                            👥 Followers: ${info.followers_count || 'N/A'}<br>
                            ➡️ Following: ${info.follows_count || 'N/A'}<br>
                            📸 Posts: ${info.media_count || 'N/A'}<br>
                            🔗 Account Type: ${info.account_type || 'N/A'}<br>
                            ✅ Verified: ${info.is_verified ? 'Yes' : 'No'}
                        </div>
                    `;
                } else {
                    const errorMsg = data.error || data.message || 'Failed to get account info';
                    let helpText = '';
                    let debugInfo = '';

                    if (errorMsg.includes('No Instagram account connected')) {
                        helpText = `
                            <br><br><strong>💡 Troubleshooting Steps:</strong><br>
                            1. <strong>Check Connection Status:</strong> Click "Check Connection Status" above<br>
                            2. <strong>Reconnect Account:</strong> Try connecting your Instagram account again<br>
                            3. <strong>Complete Verification:</strong> If you see verification prompts, complete them<br>
                            4. <strong>Wait and Retry:</strong> Sometimes it takes a few minutes for accounts to appear<br>
                            5. <strong>Check Unipile Dashboard:</strong> Visit <a href="https://app.unipile.com/dashboard" target="_blank">Unipile Dashboard</a> to verify your account
                        `;
                    }

                    // Show debug info if available
                    if (data.debug_data || data.debug_info) {
                        const debug = data.debug_data || data.debug_info;
                        debugInfo = `<br><br><small><strong>Debug Info:</strong> ${JSON.stringify(debug, null, 2)}</small>`;
                    }

                    resultDiv.innerHTML = `
                        <div class="error">
                            ❌ ${errorMsg}${helpText}${debugInfo}
                        </div>
                    `;
                }
            })
            .catch(error => {
                document.getElementById('accountInfoResult').innerHTML =
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function sendTestMessage() {
            const userId = document.getElementById('testUserId').value;
            const message = document.getElementById('testMessage').value;
            const resultDiv = document.getElementById('testResult');

            if (!userId || !message) {
                resultDiv.innerHTML = '<div class="error">❌ Please enter both user ID and message</div>';
                return;
            }

            // First check if Instagram account is connected
            resultDiv.innerHTML = '<div class="info">🔄 Checking Instagram account connection...</div>';

            fetch('/api/instagram/connection-status')
            .then(response => response.json())
            .then(statusData => {
                if (!statusData.success || !statusData.status.unipile.connected || statusData.status.unipile.accounts.length === 0) {
                    resultDiv.innerHTML = `
                        <div class="error">
                            ❌ No Instagram account connected!<br>
                            <strong>Please connect your Instagram account first:</strong><br>
                            1. Enter your Instagram credentials above<br>
                            2. Click "Connect Instagram Account"<br>
                            3. Complete any verification steps<br>
                            4. Wait for account to appear in "Connection Status"<br>
                            5. Then try sending the message again
                        </div>
                    `;
                    return;
                }

                // Account is connected, proceed with sending message
                resultDiv.innerHTML = '<div class="info">📤 Sending message via Unipile...</div>';

                fetch('/api/instagram/send-message', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        recipient_id: userId,
                        message: message,
                        use_unipile: true
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const method = data.method || 'Unipile';
                        resultDiv.innerHTML = `
                            <div class="success">
                                ✅ Test message sent successfully!<br>
                                <small>Method: ${method}</small>
                            </div>
                        `;
                    } else {
                        // Provide specific error handling
                        let errorMessage = data.error || 'Unknown error';
                        let helpText = '';

                        if (errorMessage.includes('No Instagram account connected')) {
                            helpText = '<br><strong>Solution:</strong> Make sure your Instagram account is connected via Unipile above.';
                        } else if (errorMessage.includes('User not found')) {
                            helpText = '<br><strong>Solution:</strong> Check that the User ID is correct. Use Instagram-scoped user ID (IGSID).';
                        }

                        resultDiv.innerHTML = `
                            <div class="error">
                                ❌ Failed to send test message<br>
                                <strong>Error:</strong> ${errorMessage}${helpText}
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    resultDiv.innerHTML = `
                        <div class="error">
                            ❌ Message error: ${error.message}<br>
                            <strong>Tip:</strong> Make sure the server is running and try again.
                        </div>
                    `;
                });
            })
            .catch(error => {
                resultDiv.innerHTML = `
                    <div class="error">
                        ❌ Failed to check account status: ${error.message}<br>
                        <strong>Tip:</strong> Make sure the server is running and try again.
                    </div>
                `;
            });
        }

        function sendBulkMessages() {
            const recipients = document.getElementById('bulkRecipients').value.split('\n').filter(id => id.trim());
            const message = document.getElementById('bulkMessage').value;
            const delay = parseFloat(document.getElementById('messageDelay').value) || 3;

            if (recipients.length === 0 || !message) {
                document.getElementById('bulkResult').innerHTML =
                    '<div class="error">❌ Please enter recipient IDs and message</div>';
                return;
            }

            // Show sending status
            document.getElementById('bulkResult').innerHTML =
                `<div class="info">📤 Sending bulk messages to ${recipients.length} recipients...</div>`;

            fetch('/api/instagram/send-bulk', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    recipients: recipients,
                    message: message,
                    delay: delay
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('bulkResult');
                if (data.success) {
                    const results = data.results || [];
                    const successful = results.filter(r => !r.result.error).length;
                    const failed = results.length - successful;

                    let html = `<div class="success">✅ Bulk messaging completed!<br>`;
                    html += `Successful: ${successful}, Failed: ${failed}<br><br>`;
                    html += '<strong>Details:</strong><br>';

                    results.forEach((result, index) => {
                        const status = result.result.error ? '❌' : '✅';
                        const method = result.method || 'unknown';
                        html += `${status} ${result.recipient_id} (${method})<br>`;
                    });

                    html += '</div>';
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Bulk messaging failed: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('bulkResult').innerHTML =
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }

        function replyToComment() {
            const commentId = document.getElementById('commentId').value;
            const replyText = document.getElementById('commentReply').value;

            if (!commentId || !replyText) {
                document.getElementById('commentReplyResult').innerHTML =
                    '<div class="error">❌ Please enter comment ID and reply text</div>';
                return;
            }

            fetch('/api/instagram/reply-comment', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    comment_id: commentId,
                    reply_text: replyText
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('commentReplyResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Comment reply sent successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to reply: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('commentReplyResult').innerHTML =
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }

        function getPostComments() {
            const postId = document.getElementById('postId').value;

            if (!postId) {
                document.getElementById('commentsResult').innerHTML =
                    '<div class="error">❌ Please enter post ID</div>';
                return;
            }

            fetch('/api/instagram/post-comments?post_id=' + postId)
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('commentsResult');
                if (data.success && data.data.data) {
                    const comments = data.data.data;
                    if (comments.length === 0) {
                        resultDiv.innerHTML = '<div class="info">No comments found on this post.</div>';
                    } else {
                        let html = '<div class="success"><strong>Recent Comments:</strong><br>';
                        comments.slice(0, 5).forEach(comment => {
                            html += `<strong>@${comment.username}:</strong> ${comment.text}<br>`;
                            html += `<small>ID: ${comment.id} | ${comment.timestamp}</small><br><br>`;
                        });
                        html += '</div>';
                        resultDiv.innerHTML = html;
                    }
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to get comments: ' + (data.error || 'Unknown error') + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('commentsResult').innerHTML =
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }

        function createPost() {
            const imageUrl = document.getElementById('imageUrl').value;
            const caption = document.getElementById('caption').value;
            
            if (!imageUrl) {
                document.getElementById('contentResult').innerHTML = 
                    '<div class="error">❌ Please enter an image URL</div>';
                return;
            }
            
            fetch('/api/instagram/create-post', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    image_url: imageUrl,
                    caption: caption
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('contentResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Instagram post created successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to create post: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('contentResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function createStory() {
            const imageUrl = document.getElementById('imageUrl').value;
            
            if (!imageUrl) {
                document.getElementById('contentResult').innerHTML = 
                    '<div class="error">❌ Please enter an image URL</div>';
                return;
            }
            
            fetch('/api/instagram/create-story', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    media_url: imageUrl
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('contentResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Instagram story created successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to create story: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('contentResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function getConversations() {
            fetch('/api/instagram/conversations')
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('conversationsResult');
                if (data.success && data.data.data) {
                    const conversations = data.data.data;
                    if (conversations.length === 0) {
                        resultDiv.innerHTML = '<div class="info">No conversations found.</div>';
                    } else {
                        let html = '<div class="success"><strong>Recent Conversations:</strong><br>';
                        conversations.slice(0, 5).forEach(conv => {
                            html += `Conversation ID: ${conv.id}, Participants: ${conv.participants?.data?.length || 0}<br>`;
                        });
                        html += '</div>';
                        resultDiv.innerHTML = html;
                    }
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to get conversations: ' + (data.error || 'Unknown error') + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('conversationsResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }

        // Auto-check status on page load
        window.onload = function() {
            checkUnipileStatus();
        };
    </script>
</body>
</html>
