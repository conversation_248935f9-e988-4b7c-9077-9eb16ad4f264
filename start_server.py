#!/usr/bin/env python3
"""
Simple server starter for Instagram integration
"""

import sys
import os

# Add integrations to path
sys.path.append('integrations')

def main():
    try:
        print("🚀 Starting Instagram Integration Server...")
        print("📍 Server will be available at: http://localhost:8000")
        print("📱 Instagram setup: http://localhost:8000/integrations/instagram_integration/instagram_auth.html")
        print("-" * 60)
        
        # Import and run
        import uvicorn
        from api_endpoints import app
        
        print("✅ Starting server...")
        
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            log_level="info"
        )
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure you have installed the requirements:")
        print("   pip install fastapi uvicorn")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Server error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
