#!/usr/bin/env python3
"""
Detailed debugging for Instagram connection
"""

import sys
import os
import json

# Add integrations to path
sys.path.append('integrations')

def test_connection_step_by_step():
    """Test each step of the connection process"""
    print("🔍 Detailed Instagram Connection Debug")
    print("=" * 50)
    
    try:
        # Step 1: Import and initialize
        print("Step 1: Importing Instagram API...")
        from instagram_integration.instagram_api import InstagramMessaging
        print("✅ Import successful")
        
        print("Step 2: Initializing Instagram API...")
        instagram = InstagramMessaging()
        print("✅ Initialization successful")
        
        # Step 3: Check Unipile client
        print("Step 3: Checking Unipile client...")
        if not instagram.unipile_client:
            print("❌ Unipile client not available")
            return False
        print("✅ Unipile client available")
        
        # Step 4: Check API key
        print("Step 4: Checking API key...")
        api_key = instagram.unipile_client.api_key
        print(f"API key (first 10 chars): {api_key[:10]}...")
        print(f"API key length: {len(api_key)}")
        
        # Step 5: Check _make_request method
        print("Step 5: Checking _make_request method...")
        if hasattr(instagram.unipile_client, '_make_request'):
            print("✅ _make_request method exists")
        else:
            print("❌ _make_request method missing")
            return False
        
        # Step 6: Test basic API call
        print("Step 6: Testing basic API call...")
        try:
            accounts = instagram.unipile_client.get_accounts()
            print(f"Basic API call successful: {type(accounts)}")
            print(f"Response keys: {list(accounts.keys()) if isinstance(accounts, dict) else 'Not a dict'}")
        except Exception as e:
            print(f"❌ Basic API call failed: {e}")
            return False
        
        # Step 7: Test _make_request directly
        print("Step 7: Testing _make_request directly...")
        try:
            test_result = instagram.unipile_client._make_request("GET", "api/v1/accounts")
            print(f"Direct _make_request successful: {type(test_result)}")
            print(f"Response: {json.dumps(test_result, indent=2)}")
        except Exception as e:
            print(f"❌ Direct _make_request failed: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # Step 8: Test connection data preparation
        print("Step 8: Testing connection data preparation...")
        test_username = "test_user"
        test_password = "test_pass"
        
        connect_data = {
            "provider": "INSTAGRAM",
            "username": test_username,
            "password": test_password
        }
        print(f"Connection data: {connect_data}")
        
        # Step 9: Test the actual connection call (without real credentials)
        print("Step 9: Testing connection endpoint...")
        try:
            # Don't actually call with real credentials, just test the endpoint format
            endpoint = "api/v1/accounts"
            print(f"Connection endpoint: {endpoint}")
            print("✅ Connection endpoint format correct")
        except Exception as e:
            print(f"❌ Connection endpoint test failed: {e}")
            return False
        
        print("\n🎉 All preliminary tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Error in step-by-step test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_actual_connection():
    """Test actual connection with dummy data to see the error"""
    print("\n🧪 Testing Actual Connection Process")
    print("=" * 40)
    
    try:
        from instagram_integration.instagram_api import InstagramMessaging
        
        instagram = InstagramMessaging()
        
        # Test with dummy credentials to see what error we get
        print("Testing connection with dummy credentials...")
        result = instagram.connect_account("dummy_user", "dummy_pass")
        
        print(f"Connection result: {json.dumps(result, indent=2)}")
        
        if "error" in result:
            print(f"\n🔍 Error details:")
            print(f"Error: {result.get('error')}")
            print(f"Message: {result.get('message')}")
            
            # Check if it's an API-related error or method error
            error_str = str(result.get('error', ''))
            if 'make_request' in error_str:
                print("❌ Still a method name issue!")
                return False
            elif 'credentials' in error_str.lower() or 'invalid' in error_str.lower():
                print("✅ Method works, just invalid credentials (expected)")
                return True
            else:
                print(f"🤔 Different error: {error_str}")
                return True
        else:
            print("✅ Connection method works (unexpected success with dummy data)")
            return True
            
    except Exception as e:
        print(f"❌ Exception in actual connection test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 Detailed Instagram Connection Debugging")
    print("🎯 This will test each step of the connection process")
    print()
    
    # Run step-by-step test
    step_success = test_connection_step_by_step()
    
    if step_success:
        # Run actual connection test
        connection_success = test_actual_connection()
        
        print("\n" + "="*50)
        print("🏁 Debug Results:")
        print(f"Step-by-step test: {'✅ PASS' if step_success else '❌ FAIL'}")
        print(f"Connection test: {'✅ PASS' if connection_success else '❌ FAIL'}")
        
        if step_success and connection_success:
            print("\n🎉 Connection method is working!")
            print("The error you're seeing is likely due to:")
            print("1. Invalid credentials")
            print("2. Instagram requiring verification")
            print("3. API rate limiting")
            print("\nTry with your real Instagram credentials.")
        else:
            print("\n⚠️  There's still an issue with the connection method.")
    else:
        print("\n❌ Basic setup failed. Check the errors above.")
