#!/usr/bin/env python3
"""
Test script to verify the new API key works across all integrations
"""

import sys
import os
import json

# Add integrations to path
sys.path.append('integrations')

def test_new_api_key():
    """Test the new API key with Instagram integration"""
    print("🔧 Testing New API Key: bs8WGcCp./sF0oPlTIohHPT6hI+hsyO2HFyGzKIKdfC36gAvXTk0=")
    print("=" * 70)
    
    try:
        # Test 1: Direct UnipileAPI test
        print("1. Testing UnipileAPI directly...")
        from unipile_api import UnipileAPI
        
        new_api_key = "bs8WGcCp./sF0oPlTIohHPT6hI+hsyO2HFyGzKIKdfC36gAvXTk0="
        unipile = UnipileAPI(api_key=new_api_key)
        
        print(f"✅ UnipileAPI initialized with new key")
        print(f"🔑 API Key: {new_api_key[:20]}...")
        
        # Test connection
        print("\n2. Testing API connection...")
        accounts = unipile.get_accounts(force_refresh=True)
        
        if "error" in accounts:
            print(f"❌ API Error: {accounts['error']}")
            return False
        else:
            print(f"✅ API Connection successful")
            print(f"📊 Total accounts: {len(accounts.get('items', []))}")
            
            # Show account details
            for i, acc in enumerate(accounts.get('items', [])):
                account_type = acc.get('type', 'Unknown')
                username = acc.get('username', 'N/A')
                account_id = acc.get('id', 'N/A')
                print(f"   Account {i+1}: {account_type} - {username} (ID: {account_id})")
        
        # Test 3: Instagram integration
        print("\n3. Testing Instagram integration...")
        from instagram_integration.instagram_api import InstagramMessaging
        
        instagram = InstagramMessaging()
        print(f"✅ Instagram API initialized")
        
        # Test account info
        account_info = instagram.get_account_info()
        if account_info.get("success"):
            print(f"✅ Instagram account found: {account_info.get('username', 'N/A')}")
        else:
            print(f"⚠️ No Instagram accounts: {account_info.get('error', 'Unknown')}")
        
        # Test connection status
        status = instagram.get_connection_status()
        instagram_connected = status.get("unipile", {}).get("connected", False)
        instagram_accounts = status.get("unipile", {}).get("accounts", [])
        
        print(f"📊 Instagram Status:")
        print(f"   Connected: {instagram_connected}")
        print(f"   Accounts: {len(instagram_accounts)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_connection_with_dummy_credentials():
    """Test Instagram connection with dummy credentials to see error handling"""
    print("\n4. Testing Instagram connection with dummy credentials...")
    
    try:
        from instagram_integration.instagram_api import InstagramMessaging
        
        instagram = InstagramMessaging()
        result = instagram.connect_account("test_user", "test_password")
        
        print(f"Connection result: {result.get('message', 'No message')}")
        
        if result.get("success"):
            print("✅ Unexpected success with dummy credentials")
        elif "401" in str(result.get("error", "")) or "Unauthorized" in str(result.get("error", "")):
            print("❌ Still getting 401 Unauthorized - API key issue not resolved")
            return False
        elif result.get("html_only_solution"):
            print("✅ HTML-only solution provided (expected for dummy credentials)")
        else:
            print(f"⚠️ Different error: {result.get('error', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 New API Key Test Suite")
    print("=" * 50)
    
    # Test 1: API key functionality
    test1_passed = test_new_api_key()
    
    # Test 2: Connection handling
    test2_passed = test_connection_with_dummy_credentials()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    print(f"✅ API Key Test: {'PASS' if test1_passed else 'FAIL'}")
    print(f"✅ Connection Test: {'PASS' if test2_passed else 'FAIL'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 New API key is working correctly!")
        print("\n💡 Next steps:")
        print("1. Try connecting your real Instagram account via the HTML interface")
        print("2. The 401 Unauthorized errors should be resolved")
        print("3. Account connections should work properly now")
    else:
        print("\n❌ API key test failed. Please check:")
        print("1. Verify the API key is correct")
        print("2. Check if the API key has proper permissions")
        print("3. Ensure the API key is for the correct Unipile environment")

if __name__ == "__main__":
    main()
