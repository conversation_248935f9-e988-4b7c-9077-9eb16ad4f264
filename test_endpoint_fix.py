#!/usr/bin/env python3
"""
Quick test of the endpoint fix
"""

import sys
import os
import json

# Add integrations to path
sys.path.append('integrations')

def test_endpoint_fix():
    """Test the endpoint fix"""
    print("🧪 Testing Endpoint Fix")
    print("=" * 30)
    
    try:
        from instagram_integration.instagram_api import InstagramMessaging
        
        instagram = InstagramMessaging()
        
        # Test with dummy credentials to see if we get a different error
        print("Testing connection with dummy credentials...")
        result = instagram.connect_account("test_user", "test_pass")
        
        print(f"Result: {json.dumps(result, indent=2)}")
        
        error_msg = str(result.get('error', ''))
        
        if '404' in error_msg and 'Cannot POST /accounts' in error_msg:
            print("❌ Still getting 404 error - endpoint not fixed")
            return False
        elif 'credentials' in error_msg.lower() or 'invalid' in error_msg.lower() or 'authentication' in error_msg.lower():
            print("✅ Endpoint fixed! Now getting authentication error (expected)")
            return True
        else:
            print(f"✅ Different error (endpoint likely fixed): {error_msg}")
            return True
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

if __name__ == "__main__":
    success = test_endpoint_fix()
    
    if success:
        print("\n🎉 Endpoint fix successful!")
        print("You can now try connecting your Instagram account again.")
    else:
        print("\n❌ Endpoint still has issues.")
