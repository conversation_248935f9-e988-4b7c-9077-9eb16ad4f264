#!/usr/bin/env python3
"""
Test the improved error handling for Instagram connection
"""

import sys
import os
import json

# Add integrations to path
sys.path.append('integrations')

def test_improved_error_handling():
    """Test the improved error handling with dummy credentials"""
    print("🔧 Testing Improved Instagram Error Handling")
    print("=" * 60)
    
    try:
        from instagram_integration.instagram_api import InstagramMessaging
        
        print("1. Initializing Instagram API...")
        instagram = InstagramMessaging()
        
        print(f"✅ Instagram API initialized")
        print(f"🔑 API Key: {instagram.unipile_client.api_key[:20]}...")
        
        print("\n2. Testing connection with dummy credentials...")
        result = instagram.connect_account("test_user", "test_password")
        
        print(f"📊 Connection result keys: {list(result.keys())}")
        print(f"📝 Message: {result.get('message', 'No message')}")
        
        # Check for improved error handling
        if result.get("manual_connection_required"):
            print("✅ Manual connection requirement detected")
        
        if result.get("html_only_solution"):
            print("✅ HTML-only solution provided")
        
        if result.get("troubleshooting"):
            troubleshooting = result.get("troubleshooting", {})
            print("✅ Troubleshooting information provided:")
            
            if troubleshooting.get("primary_issue"):
                print(f"   🔍 Primary issue: {troubleshooting['primary_issue']}")
            
            if troubleshooting.get("step_by_step_manual_connection"):
                steps = troubleshooting["step_by_step_manual_connection"]
                print(f"   🔧 Manual connection steps: {len(steps)} steps provided")
            
            if troubleshooting.get("immediate_solutions"):
                solutions = troubleshooting["immediate_solutions"]
                print(f"   💡 Immediate solutions: {len(solutions)} solutions provided")
            
            if troubleshooting.get("account_requirements"):
                requirements = troubleshooting["account_requirements"]
                print(f"   📋 Account requirements: {len(requirements)} requirements listed")
            
            if troubleshooting.get("api_limitations"):
                limitations = troubleshooting["api_limitations"]
                print(f"   ⚠️ API limitations: {len(limitations)} limitations explained")
        
        if result.get("dashboard_url"):
            print(f"✅ Dashboard URL provided: {result['dashboard_url']}")
        
        # Test the old error message is not present
        if "Unable to connect Instagram account using available methods." in result.get("message", ""):
            print("❌ Old generic error message still present")
            return False
        else:
            print("✅ Improved error message provided")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_qr_code_removal():
    """Test that QR code generation properly explains Instagram doesn't support it"""
    print("\n3. Testing QR code handling...")
    
    try:
        from instagram_integration.instagram_api import InstagramMessaging
        
        instagram = InstagramMessaging()
        qr_result = instagram.generate_qr_code()
        
        print(f"📊 QR result keys: {list(qr_result.keys())}")
        print(f"📝 Message: {qr_result.get('message', 'No message')}")
        
        if not qr_result.get("success"):
            print("✅ QR code correctly returns failure")
        
        if "not supported" in qr_result.get("message", "").lower():
            print("✅ Clear explanation that Instagram doesn't support QR codes")
        
        if qr_result.get("alternative_methods"):
            alternatives = qr_result["alternative_methods"]
            print(f"✅ Alternative methods provided: {len(alternatives)} alternatives")
        
        return True
        
    except Exception as e:
        print(f"❌ QR code test error: {e}")
        return False

def main():
    """Run all improved error handling tests"""
    print("🚀 Improved Instagram Error Handling Test")
    print("=" * 60)
    
    # Test 1: Improved error handling
    test1_passed = test_improved_error_handling()
    
    # Test 2: QR code handling
    test2_passed = test_qr_code_removal()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Summary:")
    print(f"✅ Improved Error Handling: {'PASS' if test1_passed else 'FAIL'}")
    print(f"✅ QR Code Handling: {'PASS' if test2_passed else 'FAIL'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 Improved Error Handling Working!")
        print("\n💡 What's improved:")
        print("• ✅ Clear explanation of API limitations")
        print("• ✅ Step-by-step manual connection guide")
        print("• ✅ Comprehensive troubleshooting information")
        print("• ✅ Account requirements clearly listed")
        print("• ✅ Direct link to Unipile Dashboard")
        print("• ✅ No more generic 'Unable to connect' messages")
        print("• ✅ Instagram QR code limitation explained")
        
        print("\n🚀 Next steps:")
        print("1. Start your server and try the HTML interface")
        print("2. You'll now get detailed guidance when connection fails")
        print("3. Follow the manual connection steps provided")
        print("4. The error messages will guide you to the solution")
        
    else:
        print("\n❌ Some improvements failed to implement correctly")
        print("Please check the specific errors above")

if __name__ == "__main__":
    main()
