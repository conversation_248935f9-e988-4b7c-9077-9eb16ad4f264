#!/usr/bin/env python3
"""
Test the method name fix
"""

import sys
import os

# Add integrations to path
sys.path.append('integrations')

def test_method_fix():
    """Test that the method names are correct"""
    print("🧪 Testing Method Name Fix")
    print("=" * 30)
    
    try:
        # Import Instagram API
        from instagram_integration.instagram_api import InstagramMessaging
        
        print("✅ Instagram API imported")
        
        # Initialize
        instagram = InstagramMessaging()
        print("✅ Instagram API initialized")
        
        # Check Unipile client
        if not instagram.unipile_client:
            print("❌ Unipile client not available")
            return False
        
        print("✅ Unipile client available")
        
        # Check if _make_request method exists
        if hasattr(instagram.unipile_client, '_make_request'):
            print("✅ _make_request method exists")
        else:
            print("❌ _make_request method missing")
            return False
        
        # Check if connect_account method exists
        if hasattr(instagram, 'connect_account'):
            print("✅ connect_account method exists")
        else:
            print("❌ connect_account method missing")
            return False
        
        # Check if solve_checkpoint method exists
        if hasattr(instagram, 'solve_checkpoint'):
            print("✅ solve_checkpoint method exists")
        else:
            print("❌ solve_checkpoint method missing")
            return False
        
        print("\n🎉 All methods are available and correctly named!")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 Method Name Fix Test")
    print("🎯 This will verify the method names are correct")
    print()
    
    success = test_method_fix()
    
    if success:
        print("\n✅ Fix successful! You can now try connecting your Instagram account again.")
        print("\n💡 Next steps:")
        print("1. Go back to your Instagram HTML page")
        print("2. Try connecting your account again")
        print("3. The connection should work properly now")
    else:
        print("\n❌ Fix failed. Check the errors above.")
    
    print("\n🔗 Your server should still be running at http://localhost:8000")
