"""
Unipile API Integration
Unified API client for multiple social media platforms via Unipile
Supports: WhatsApp, Telegram, Facebook, Instagram, LinkedIn
"""

import requests
import json
import time
from typing import Dict, List, Optional
import logging
from datetime import datetime

class UnipileAPI:
    def __init__(self, api_key: str = "bs8WGcCp./sF0oPlTIohHPT6hI+hsyO2HFyGzKIKdfC36gAvXTk0=",
                 base_url: str = "https://api8.unipile.com:13814"):
        """Initialize Unipile API client"""
        self.api_key = api_key
        self.base_url = base_url
        
        # Default headers
        self.headers = {
            "X-API-KEY": self.api_key,
            "accept": "application/json",
            "Content-Type": "application/json"
        }
        
        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 0.5  # 2 requests per second
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # Cache for accounts
        self._accounts_cache = None
        self._cache_timestamp = 0
        self._cache_duration = 300  # 5 minutes
    
    def _rate_limit(self):
        """Implement rate limiting"""
        current_time = time.time()
        time_since_last_request = current_time - self.last_request_time
        
        if time_since_last_request < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last_request
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def _make_request(self, method: str, endpoint: str, data: Dict = None, 
                     params: Dict = None) -> Dict:
        """Make HTTP request with error handling"""
        self._rate_limit()
        
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        
        try:
            if method.upper() == "GET":
                response = requests.get(url, headers=self.headers, params=params)
            elif method.upper() == "POST":
                response = requests.post(url, headers=self.headers, json=data, params=params)
            elif method.upper() == "PUT":
                response = requests.put(url, headers=self.headers, json=data, params=params)
            elif method.upper() == "DELETE":
                response = requests.delete(url, headers=self.headers, params=params)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            response.raise_for_status()
            return response.json() if response.content else {"success": True}
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Request failed: {e}")
            if hasattr(e, 'response') and e.response is not None:
                status_code = e.response.status_code
                try:
                    error_data = e.response.json()
                    if status_code == 403:
                        return {"error": f"Access forbidden (403): Invalid or expired API key. Please check your Unipile API key."}
                    elif status_code == 401:
                        return {"error": f"Unauthorized (401): Invalid API key or authentication failed."}
                    else:
                        return {"error": error_data}
                except:
                    if status_code == 403:
                        return {"error": f"Access forbidden (403): Invalid or expired API key. Please check your Unipile API key."}
                    elif status_code == 401:
                        return {"error": f"Unauthorized (401): Invalid API key or authentication failed."}
                    else:
                        return {"error": f"HTTP {status_code}: {str(e)}"}
            return {"error": str(e)}
    
    def get_accounts(self, force_refresh: bool = False) -> Dict:
        """Get all connected accounts"""
        current_time = time.time()

        # Use cache if available and not expired
        if (not force_refresh and
            self._accounts_cache and
            current_time - self._cache_timestamp < self._cache_duration):
            return self._accounts_cache

        result = self._make_request("GET", "api/v1/accounts")

        if "error" not in result:
            self._accounts_cache = result
            self._cache_timestamp = current_time
            self.logger.info("Accounts retrieved successfully")
        else:
            self.logger.error(f"Failed to get accounts: {result}")

        return result
    
    def get_account_by_platform(self, platform: str) -> Optional[Dict]:
        """Get account by platform name"""
        # Validate platform parameter
        if not isinstance(platform, str):
            self.logger.error(f"Platform must be a string, got {type(platform)}: {platform}")
            return None

        accounts = self.get_accounts()

        if "error" in accounts:
            return None

        # Look for account with matching platform
        # Unipile uses "type" field for platform identification
        platform_mapping = {
            "whatsapp": "WHATSAPP",
            "telegram": "TELEGRAM",
            "facebook": "FACEBOOK",
            "instagram": "INSTAGRAM",
            "linkedin": "LINKEDIN",
            "tiktok": "TIKTOK"
        }

        target_type = platform_mapping.get(platform.lower())
        if not target_type:
            return None

        for account in accounts.get("items", []):
            if account.get("type", "").upper() == target_type:
                return account

        return None
    


    def send_message(self, account_id: str, recipient_id: str, text: str,
                    message_type: str = "text", **kwargs) -> Dict:
        """Send message via Unipile API using chat-based approach"""
        try:
            # First, try to find existing chat with this recipient
            chats = self.get_chats(account_id)
            existing_chat = None

            if "error" not in chats:
                for chat in chats.get("items", []):
                    # Check if this chat is with the target recipient
                    attendees = chat.get("attendees", [])
                    for attendee in attendees:
                        if attendee.get("identifier") == recipient_id or attendee.get("phone_number") == recipient_id:
                            existing_chat = chat
                            break
                    if existing_chat:
                        break

            # If no existing chat found, start a new one
            if not existing_chat:
                chat_data = {
                    "account_id": account_id,
                    "attendees_ids": [recipient_id],
                    "text": text,
                    "type": message_type
                }
                chat_data.update(kwargs)

                result = self._make_request("POST", "api/v1/chats", chat_data)

                if "error" not in result:
                    self.logger.info(f"New chat started and message sent to {recipient_id}")
                else:
                    self.logger.error(f"Failed to start chat and send message: {result}")

                return result
            else:
                # Send message to existing chat
                chat_id = existing_chat.get("id")
                message_data = {
                    "text": text,
                    "type": message_type
                }
                message_data.update(kwargs)

                result = self._make_request("POST", f"api/v1/chats/{chat_id}/messages", message_data)

                if "error" not in result:
                    self.logger.info(f"Message sent to existing chat {chat_id} for {recipient_id}")
                else:
                    self.logger.error(f"Failed to send message to chat {chat_id}: {result}")

                return result

        except Exception as e:
            self.logger.error(f"Exception in send_message: {e}")
            return {"error": str(e)}
    
    def generate_whatsapp_qr(self) -> Dict:
        """Generate WhatsApp QR code for authentication"""
        data = {
            "provider": "WHATSAPP"
        }

        result = self._make_request("POST", "api/v1/accounts", data)

        if "error" not in result:
            self.logger.info("WhatsApp QR code generated successfully")
        else:
            self.logger.error(f"Failed to generate WhatsApp QR code: {result}")

        return result

    def send_whatsapp_message(self, recipient_phone: str, text: str, **kwargs) -> Dict:
        """Send WhatsApp message"""
        whatsapp_account = self.get_account_by_platform("whatsapp")

        if not whatsapp_account:
            return {"error": "WhatsApp account not found or not connected"}

        account_id = whatsapp_account.get("id") or whatsapp_account.get("account_id")
        return self.send_message(account_id, recipient_phone, text, **kwargs)

    def send_telegram_message(self, chat_id: str, text: str, **kwargs) -> Dict:
        """Send Telegram message"""
        telegram_account = self.get_account_by_platform("telegram")

        if not telegram_account:
            return {"error": "Telegram account not found or not connected"}

        account_id = telegram_account.get("id") or telegram_account.get("account_id")
        return self.send_message(account_id, chat_id, text, **kwargs)

    def send_facebook_message(self, recipient_id: str, text: str, **kwargs) -> Dict:
        """Send Facebook Messenger message"""
        facebook_account = self.get_account_by_platform("facebook")

        if not facebook_account:
            return {"error": "Facebook account not found or not connected"}

        account_id = facebook_account.get("id") or facebook_account.get("account_id")
        return self.send_message(account_id, recipient_id, text, **kwargs)

    def send_instagram_message(self, recipient_id: str, text: str, **kwargs) -> Dict:
        """Send Instagram direct message"""
        instagram_account = self.get_account_by_platform("instagram")

        if not instagram_account:
            return {"error": "Instagram account not found or not connected"}

        account_id = instagram_account.get("id") or instagram_account.get("account_id")
        return self.send_message(account_id, recipient_id, text, **kwargs)

    def send_linkedin_message(self, recipient_id: str, text: str, **kwargs) -> Dict:
        """Send LinkedIn message"""
        linkedin_account = self.get_account_by_platform("linkedin")

        if not linkedin_account:
            return {"error": "LinkedIn account not found or not connected"}

        account_id = linkedin_account.get("id") or linkedin_account.get("account_id")
        return self.send_message(account_id, recipient_id, text, **kwargs)

    def send_tiktok_message(self, recipient_id: str, text: str, **kwargs) -> Dict:
        """Send TikTok message"""
        tiktok_account = self.get_account_by_platform("tiktok")

        if not tiktok_account:
            return {"error": "TikTok account not found or not connected"}

        account_id = tiktok_account.get("id") or tiktok_account.get("account_id")
        return self.send_message(account_id, recipient_id, text, **kwargs)
    
    def get_chats(self, account_id: str, limit: int = 50) -> Dict:
        """Get chats for an account"""
        params = {
            "account_id": account_id,
            "limit": limit
        }

        return self._make_request("GET", "api/v1/chats", params=params)

    def get_messages(self, account_id: str, chat_id: str, limit: int = 50) -> Dict:
        """Get messages from a chat"""
        params = {
            "account_id": account_id,
            "limit": limit
        }

        return self._make_request("GET", f"api/v1/chats/{chat_id}/messages", params=params)

    def start_new_chat(self, account_id: str, recipient_id: str, initial_message: str = None, **kwargs) -> Dict:
        """Start a new chat with a recipient"""
        data = {
            "account_id": account_id,
            "attendees_ids": [recipient_id]
        }

        # Add initial message if provided
        if initial_message:
            data["text"] = initial_message
            data["type"] = kwargs.get("message_type", "text")

        # Add any additional parameters
        data.update(kwargs)

        return self._make_request("POST", "api/v1/chats", data)

    def send_message_to_chat(self, chat_id: str, text: str, message_type: str = "text", **kwargs) -> Dict:
        """Send a message to an existing chat"""
        data = {
            "text": text,
            "type": message_type
        }

        # Add any additional parameters
        data.update(kwargs)

        return self._make_request("POST", f"api/v1/chats/{chat_id}/messages", data)

    def upload_media(self, account_id: str, file_path: str, media_type: str = "image") -> Dict:
        """Upload media file"""
        # This would need to be implemented based on Unipile's media upload API
        # For now, return a placeholder
        return {"error": "Media upload not implemented yet"}
    
    def send_media_message(self, account_id: str, recipient_id: str,
                          media_url: str, caption: str = "", media_type: str = "image") -> Dict:
        """Send media message using chat-based approach"""
        try:
            # First, try to find existing chat with this recipient
            chats = self.get_chats(account_id)
            existing_chat = None

            if "error" not in chats:
                for chat in chats.get("items", []):
                    # Check if this chat is with the target recipient
                    attendees = chat.get("attendees", [])
                    for attendee in attendees:
                        if attendee.get("identifier") == recipient_id or attendee.get("phone_number") == recipient_id:
                            existing_chat = chat
                            break
                    if existing_chat:
                        break

            # If no existing chat found, start a new one with media
            if not existing_chat:
                chat_data = {
                    "account_id": account_id,
                    "attendees_ids": [recipient_id],
                    "type": media_type,
                    "media_url": media_url,
                    "text": caption
                }

                return self._make_request("POST", "api/v1/chats", chat_data)
            else:
                # Send media message to existing chat
                chat_id = existing_chat.get("id")
                message_data = {
                    "type": media_type,
                    "media_url": media_url,
                    "text": caption
                }

                return self._make_request("POST", f"api/v1/chats/{chat_id}/messages", message_data)

        except Exception as e:
            self.logger.error(f"Exception in send_media_message: {e}")
            return {"error": str(e)}

    def get_account_info(self, account_id: str) -> Dict:
        """Get detailed account information"""
        return self._make_request("GET", f"api/v1/accounts/{account_id}")
    
    def send_bulk_messages(self, messages: List[Dict], delay: float = 1.0) -> List[Dict]:
        """Send multiple messages with delay"""
        results = []
        
        for message_data in messages:
            account_id = message_data.get("account_id")
            recipient_id = message_data.get("recipient_id")
            text = message_data.get("text")
            
            if not all([account_id, recipient_id, text]):
                results.append({
                    "error": "Missing required fields: account_id, recipient_id, or text",
                    "message_data": message_data,
                    "timestamp": datetime.now().isoformat()
                })
                continue
            
            result = self.send_message(account_id, recipient_id, text)
            results.append({
                "result": result,
                "message_data": message_data,
                "timestamp": datetime.now().isoformat()
            })
            
            if delay > 0:
                time.sleep(delay)
        
        return results
    
    def get_platform_accounts(self) -> Dict[str, Optional[Dict]]:
        """Get accounts organized by platform"""
        accounts = self.get_accounts()

        if "error" in accounts:
            return {"error": accounts["error"]}

        platform_accounts = {
            "whatsapp": None,
            "telegram": None,
            "facebook": None,
            "instagram": None,
            "linkedin": None,
            "tiktok": None
        }

        # Map Unipile types to our platform names
        type_mapping = {
            "WHATSAPP": "whatsapp",
            "TELEGRAM": "telegram",
            "FACEBOOK": "facebook",
            "INSTAGRAM": "instagram",
            "LINKEDIN": "linkedin",
            "TIKTOK": "tiktok"
        }

        for account in accounts.get("items", []):
            account_type = account.get("type", "").upper()
            platform = type_mapping.get(account_type)
            if platform:
                platform_accounts[platform] = account

        return platform_accounts
    
    def is_platform_connected(self, platform: str) -> bool:
        """Check if a platform is connected"""
        account = self.get_account_by_platform(platform)
        return account is not None
    
    def get_connection_status(self) -> Dict[str, bool]:
        """Get connection status for all supported platforms"""
        return {
            "whatsapp": self.is_platform_connected("whatsapp"),
            "telegram": self.is_platform_connected("telegram"),
            "facebook": self.is_platform_connected("facebook"),
            "instagram": self.is_platform_connected("instagram"),
            "linkedin": self.is_platform_connected("linkedin"),
            "tiktok": self.is_platform_connected("tiktok")
        }

# Example usage
if __name__ == "__main__":
    # Initialize Unipile API
    unipile = UnipileAPI()
    
    # Get all accounts
    accounts = unipile.get_accounts()
    print("Accounts:", json.dumps(accounts, indent=2))
    
    # Check connection status
    status = unipile.get_connection_status()
    print("Connection Status:", status)
    
    # Example: Send WhatsApp message (uncomment to test)
    # result = unipile.send_whatsapp_message("+**********", "Hello from Unipile!")
    # print("WhatsApp Result:", result)
