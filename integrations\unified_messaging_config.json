{"system_info": {"name": "Unified Messaging System", "version": "1.0.0", "description": "Centralized messaging system for all social media platforms using Unipile API", "last_updated": "2024-01-01"}, "unipile_settings": {"enabled": true, "primary_method": true, "api_key": "bs8WGcCp./sF0oPlTIohHPT6hI+hsyO2HFyGzKIKdfC36gAvXTk0=", "base_url": "https://api8.unipile.com:13814", "timeout": 30, "max_retries": 3, "retry_delay": 2.0}, "enabled_platforms": ["whatsapp", "telegram", "facebook", "instagram", "tiktok", "linkedin"], "platform_priorities": {"whatsapp": 1, "telegram": 2, "facebook": 3, "instagram": 4, "linkedin": 5, "tiktok": 6}, "messaging_settings": {"default_delay": 2.0, "max_retries": 3, "use_unipile_primary": true, "fallback_enabled": true, "bulk_message_batch_size": 50, "concurrent_messages": 5}, "rate_limiting": {"global_delay": 1.0, "platform_delays": {"whatsapp": 1.0, "telegram": 0.5, "facebook": 2.0, "instagram": 3.0, "tiktok": 2.0, "linkedin": 3.0}, "daily_limits": {"whatsapp": 1000, "telegram": 2000, "facebook": 500, "instagram": 200, "tiktok": 100, "linkedin": 300}}, "message_templates": {"welcome": {"text": "👋 Welcome! Thanks for connecting with us on {platform}.", "platforms": ["all"]}, "follow_up": {"text": "📞 Following up on your inquiry. How can we help you today?", "platforms": ["whatsapp", "telegram", "facebook"]}, "promotional": {"text": "🎉 Special offer just for you! Check out our latest deals: {link}", "platforms": ["whatsapp", "telegram", "facebook", "instagram"]}, "networking": {"text": "🤝 Great to connect with you! Let's stay in touch and explore opportunities.", "platforms": ["linkedin", "facebook"]}, "support": {"text": "💬 Our support team is here to help. What can we assist you with?", "platforms": ["whatsapp", "telegram", "facebook"]}, "professional": {"text": "💼 Thank you for your interest in our professional services. Let's schedule a consultation.", "platforms": ["linkedin", "facebook"]}}, "platform_specific_settings": {"whatsapp": {"message_format": "text", "supports_media": true, "supports_templates": true, "business_api": true, "qr_auth": true}, "telegram": {"message_format": "text", "supports_media": true, "supports_keyboards": true, "bot_api": true, "qr_auth": false}, "facebook": {"message_format": "text", "supports_media": true, "supports_quick_replies": true, "page_api": true, "oauth_required": true}, "instagram": {"message_format": "text", "supports_media": true, "business_only": true, "comment_replies": true, "story_interactions": true}, "linkedin": {"message_format": "text", "professional_only": true, "connection_required": true, "inmail_support": true, "company_pages": true}, "tiktok": {"message_format": "text", "comment_interactions": true, "direct_messaging": false, "business_api": true, "engagement_only": true}}, "authentication_methods": {"unipile": {"method": "api_key", "required_fields": ["api_key"], "supports_qr": true, "auto_refresh": true}, "whatsapp": {"method": "qr_code", "required_fields": ["phone_number_id", "access_token"], "supports_qr": true, "webhook_required": true}, "telegram": {"method": "bot_token", "required_fields": ["bot_token"], "supports_qr": false, "webhook_optional": true}, "facebook": {"method": "o<PERSON>h", "required_fields": ["app_id", "app_secret", "page_access_token"], "supports_qr": false, "webhook_required": true}, "instagram": {"method": "o<PERSON>h", "required_fields": ["app_id", "app_secret", "access_token"], "supports_qr": false, "business_account_required": true}, "linkedin": {"method": "o<PERSON>h", "required_fields": ["client_id", "client_secret", "access_token"], "supports_qr": false, "professional_account": true}, "tiktok": {"method": "o<PERSON>h", "required_fields": ["client_key", "client_secret", "access_token"], "supports_qr": false, "business_account_required": true}}, "logging": {"level": "INFO", "log_file": "unified_messaging.log", "log_requests": true, "log_responses": false, "log_errors": true, "max_log_size": "10MB", "backup_count": 5}, "analytics": {"enabled": true, "track_message_success": true, "track_response_times": true, "track_platform_usage": true, "export_format": "json", "retention_days": 30}, "security": {"encrypt_tokens": true, "token_rotation": true, "rate_limit_protection": true, "ip_whitelist": [], "require_https": true}, "features": {"bulk_messaging": true, "scheduled_messages": true, "message_templates": true, "auto_retry": true, "fallback_routing": true, "real_time_status": true, "analytics_dashboard": true, "webhook_support": true}}